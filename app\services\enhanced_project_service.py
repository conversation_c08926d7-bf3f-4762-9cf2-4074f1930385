"""
增强的项目信息管理服务
支持项目全局信息的向量化和智能检索
"""

import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

from app.services.project_service import project_service
from app.services.worldbuilding_service import worldbuilding_service
from app.services.character_service import character_service
from app.services.ai_memory_service import get_ai_memory_service
from app.services.embedding_service import get_embedding_service
from app.models.memory import MemoryEntry, MemoryType

logger = logging.getLogger(__name__)


@dataclass
class ProjectGlobalInfo:
    """项目全局信息"""
    project_id: str
    project_name: str
    description: str
    genre: str
    
    # 世界观信息
    world_setting: str = ""
    world_rules: List[str] = None
    magic_system: str = ""
    technology_level: str = ""
    
    # 角色信息
    main_characters: List[str] = None
    character_relationships: Dict[str, List[str]] = None
    
    # 情节信息
    main_plotlines: List[str] = None
    themes: List[str] = None
    
    # 元数据
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.world_rules is None:
            self.world_rules = []
        if self.main_characters is None:
            self.main_characters = []
        if self.character_relationships is None:
            self.character_relationships = {}
        if self.main_plotlines is None:
            self.main_plotlines = []
        if self.themes is None:
            self.themes = []


@dataclass
class ProjectInfoSearchQuery:
    """项目信息搜索查询"""
    project_id: str
    query_text: str
    info_types: List[str] = None  # world, character, plot, theme
    limit: int = 10
    similarity_threshold: float = 0.7
    
    def __post_init__(self):
        if self.info_types is None:
            self.info_types = ['world', 'character', 'plot', 'theme']


@dataclass
class ProjectInfoSearchResult:
    """项目信息搜索结果"""
    info_type: str
    title: str
    content: str
    relevance_score: float
    source: str = ""
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class EnhancedProjectService:
    """增强的项目信息管理服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ai_memory = get_ai_memory_service()
        self.embedding_service = get_embedding_service()
    
    async def vectorize_project_info(self, project_id: str) -> bool:
        """向量化项目全局信息"""
        try:
            self.logger.info(f"开始向量化项目信息: {project_id}")
            
            # 获取项目基本信息
            project = project_service.get_project(project_id)
            if not project:
                raise ValueError(f"项目不存在: {project_id}")
            
            # 获取世界观信息
            worldbuilding = worldbuilding_service.get_worldbuilding(project_id)
            
            # 获取角色信息
            characters = character_service.get_characters(project_id)
            
            # 构建全局信息
            global_info = await self._build_global_info(project, worldbuilding, characters)
            
            # 分别向量化不同类型的信息
            await self._vectorize_world_info(global_info)
            await self._vectorize_character_info(global_info)
            await self._vectorize_plot_info(global_info)
            await self._vectorize_theme_info(global_info)
            
            # 保存全局信息索引
            await self._save_global_info_index(global_info)
            
            self.logger.info(f"项目信息向量化完成: {project_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"向量化项目信息失败: {e}")
            return False
    
    async def _build_global_info(self, project, worldbuilding, characters) -> ProjectGlobalInfo:
        """构建项目全局信息"""
        try:
            # 提取角色关系
            character_relationships = {}
            main_characters = []
            
            for char in characters:
                main_characters.append(char.name)
                if hasattr(char, 'relationships') and char.relationships:
                    character_relationships[char.name] = list(char.relationships.keys())
            
            # 提取世界观规则
            world_rules = []
            if worldbuilding and hasattr(worldbuilding, 'world_rules'):
                world_rules = worldbuilding.world_rules if isinstance(worldbuilding.world_rules, list) else []
            
            global_info = ProjectGlobalInfo(
                project_id=project.id,
                project_name=project.name,
                description=project.description,
                genre=project.genre,
                world_setting=getattr(worldbuilding, 'setting', '') if worldbuilding else '',
                world_rules=world_rules,
                magic_system=getattr(worldbuilding, 'magic_system', '') if worldbuilding else '',
                technology_level=getattr(worldbuilding, 'technology_level', '') if worldbuilding else '',
                main_characters=main_characters,
                character_relationships=character_relationships,
                main_plotlines=[],  # 将从章节分析中提取
                themes=[],  # 将从章节分析中提取
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            return global_info
            
        except Exception as e:
            self.logger.error(f"构建全局信息失败: {e}")
            raise
    
    async def _vectorize_world_info(self, global_info: ProjectGlobalInfo):
        """向量化世界观信息"""
        try:
            world_content = f"""
项目名称: {global_info.project_name}
类型: {global_info.genre}
世界设定: {global_info.world_setting}
魔法系统: {global_info.magic_system}
技术水平: {global_info.technology_level}
世界规则: {'; '.join(global_info.world_rules)}
"""
            
            memory_entry = MemoryEntry(
                id=f"world_info_{global_info.project_id}",
                project_id=global_info.project_id,
                type=MemoryType.WORLD,
                title=f"{global_info.project_name} - 世界观设定",
                content=world_content.strip(),
                summary=f"{global_info.genre}类型小说的世界观设定，包含魔法系统和技术水平",
                tags=['project_info', 'world_setting', 'global'],
                metadata={
                    'info_type': 'world',
                    'genre': global_info.genre,
                    'has_magic': bool(global_info.magic_system),
                    'rules_count': len(global_info.world_rules)
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            await self.ai_memory.add_memory(memory_entry)
            
        except Exception as e:
            self.logger.error(f"向量化世界观信息失败: {e}")
    
    async def _vectorize_character_info(self, global_info: ProjectGlobalInfo):
        """向量化角色信息"""
        try:
            character_content = f"""
项目名称: {global_info.project_name}
主要角色: {', '.join(global_info.main_characters)}
角色关系网络:
"""
            
            for char, relations in global_info.character_relationships.items():
                character_content += f"- {char}: 与 {', '.join(relations)} 有关系\n"
            
            memory_entry = MemoryEntry(
                id=f"character_info_{global_info.project_id}",
                project_id=global_info.project_id,
                type=MemoryType.CHARACTER,
                title=f"{global_info.project_name} - 角色关系网络",
                content=character_content.strip(),
                summary=f"包含{len(global_info.main_characters)}个主要角色的关系网络",
                tags=['project_info', 'character_network', 'global'],
                metadata={
                    'info_type': 'character',
                    'character_count': len(global_info.main_characters),
                    'relationship_count': sum(len(rels) for rels in global_info.character_relationships.values())
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            await self.ai_memory.add_memory(memory_entry)
            
        except Exception as e:
            self.logger.error(f"向量化角色信息失败: {e}")
    
    async def _vectorize_plot_info(self, global_info: ProjectGlobalInfo):
        """向量化情节信息"""
        try:
            plot_content = f"""
项目名称: {global_info.project_name}
项目描述: {global_info.description}
主要情节线: {'; '.join(global_info.main_plotlines) if global_info.main_plotlines else '待分析'}
"""
            
            memory_entry = MemoryEntry(
                id=f"plot_info_{global_info.project_id}",
                project_id=global_info.project_id,
                type=MemoryType.PLOT,
                title=f"{global_info.project_name} - 主要情节",
                content=plot_content.strip(),
                summary=f"项目的主要情节线和故事描述",
                tags=['project_info', 'main_plot', 'global'],
                metadata={
                    'info_type': 'plot',
                    'plotline_count': len(global_info.main_plotlines)
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            await self.ai_memory.add_memory(memory_entry)
            
        except Exception as e:
            self.logger.error(f"向量化情节信息失败: {e}")
    
    async def _vectorize_theme_info(self, global_info: ProjectGlobalInfo):
        """向量化主题信息"""
        try:
            theme_content = f"""
项目名称: {global_info.project_name}
类型: {global_info.genre}
主要主题: {'; '.join(global_info.themes) if global_info.themes else '待分析'}
项目描述: {global_info.description}
"""
            
            memory_entry = MemoryEntry(
                id=f"theme_info_{global_info.project_id}",
                project_id=global_info.project_id,
                type=MemoryType.WORLD,
                title=f"{global_info.project_name} - 主题思想",
                content=theme_content.strip(),
                summary=f"项目的主要主题思想和创作理念",
                tags=['project_info', 'themes', 'global'],
                metadata={
                    'info_type': 'theme',
                    'theme_count': len(global_info.themes),
                    'genre': global_info.genre
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            await self.ai_memory.add_memory(memory_entry)
            
        except Exception as e:
            self.logger.error(f"向量化主题信息失败: {e}")
    
    async def _save_global_info_index(self, global_info: ProjectGlobalInfo):
        """保存全局信息索引"""
        try:
            memory_entry = MemoryEntry(
                id=f"global_index_{global_info.project_id}",
                project_id=global_info.project_id,
                type=MemoryType.WORLD,
                title=f"{global_info.project_name} - 全局信息索引",
                content=json.dumps(asdict(global_info), ensure_ascii=False, indent=2, default=str),
                summary=f"项目全局信息索引，包含世界观、角色、情节等完整信息",
                tags=['project_info', 'global_index', 'metadata'],
                metadata={
                    'info_type': 'global_index',
                    'project_name': global_info.project_name,
                    'genre': global_info.genre
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            await self.ai_memory.add_memory(memory_entry)
            
        except Exception as e:
            self.logger.error(f"保存全局信息索引失败: {e}")
    
    async def search_project_info(self, query: ProjectInfoSearchQuery) -> List[ProjectInfoSearchResult]:
        """搜索项目信息"""
        try:
            self.logger.info(f"搜索项目信息: {query.query_text}")
            
            # 构建搜索标签
            search_tags = ['project_info']
            if 'world' in query.info_types:
                search_tags.append('world_setting')
            if 'character' in query.info_types:
                search_tags.append('character_network')
            if 'plot' in query.info_types:
                search_tags.append('main_plot')
            if 'theme' in query.info_types:
                search_tags.append('themes')
            
            # 搜索记忆
            memories = await self.ai_memory.search_memories(
                project_id=query.project_id,
                query=query.query_text,
                tags=search_tags,
                limit=query.limit,
                similarity_threshold=query.similarity_threshold
            )
            
            results = []
            for memory in memories:
                info_type = memory.metadata.get('info_type', 'unknown')
                if info_type in query.info_types or 'all' in query.info_types:
                    result = ProjectInfoSearchResult(
                        info_type=info_type,
                        title=memory.title,
                        content=memory.content,
                        relevance_score=0.8,  # 默认相关性分数
                        source='project_memory',
                        metadata=memory.metadata
                    )
                    results.append(result)
            
            self.logger.info(f"搜索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"搜索项目信息失败: {e}")
            return []


# 全局实例
enhanced_project_service = EnhancedProjectService()


def get_enhanced_project_service() -> EnhancedProjectService:
    """获取增强项目服务实例"""
    return enhanced_project_service
