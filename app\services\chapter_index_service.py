"""
章节索引服务
提供多维度的章节检索和索引功能
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from collections import defaultdict

from app.services.chapter_service import chapter_service
from app.services.ai_memory_service import get_ai_memory_service
from app.services.embedding_service import get_embedding_service
from app.models.memory import MemoryType

logger = logging.getLogger(__name__)


@dataclass
class ChapterIndex:
    """章节索引"""
    chapter_id: str
    project_id: str
    title: str
    chapter_number: Optional[int] = None
    word_count: int = 0
    
    # 内容分类
    content_types: List[str] = None  # 对话、描述、动作、心理活动等
    main_characters: List[str] = None
    scenes: List[str] = None
    plot_points: List[str] = None
    
    # 情感和主题
    emotional_tone: str = ""
    themes: List[str] = None
    
    # 元数据
    created_at: datetime = None
    updated_at: datetime = None
    vectorized: bool = False
    
    def __post_init__(self):
        if self.content_types is None:
            self.content_types = []
        if self.main_characters is None:
            self.main_characters = []
        if self.scenes is None:
            self.scenes = []
        if self.plot_points is None:
            self.plot_points = []
        if self.themes is None:
            self.themes = []


@dataclass
class ChapterSearchQuery:
    """章节搜索查询"""
    project_id: str
    query_text: str = ""
    
    # 过滤条件
    chapter_numbers: List[int] = None
    characters: List[str] = None
    scenes: List[str] = None
    content_types: List[str] = None
    themes: List[str] = None
    emotional_tone: str = ""
    
    # 搜索选项
    search_type: str = "semantic"  # semantic, keyword, hybrid
    limit: int = 10
    similarity_threshold: float = 0.7
    
    def __post_init__(self):
        if self.chapter_numbers is None:
            self.chapter_numbers = []
        if self.characters is None:
            self.characters = []
        if self.scenes is None:
            self.scenes = []
        if self.content_types is None:
            self.content_types = []
        if self.themes is None:
            self.themes = []


@dataclass
class ChapterSearchResult:
    """章节搜索结果"""
    chapter_index: ChapterIndex
    relevance_score: float
    match_reasons: List[str] = None
    content_snippet: str = ""
    
    def __post_init__(self):
        if self.match_reasons is None:
            self.match_reasons = []


class ChapterIndexService:
    """章节索引服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ai_memory = get_ai_memory_service()
        self.embedding_service = get_embedding_service()
        
        # 内容类型识别模式
        self.content_type_patterns = {
            'dialogue': [
                r'"[^"]*"',  # 双引号对话
                r''[^']*'',  # 单引号对话
                r'说道?[：:]',  # 说道、说：
                r'问道?[：:]',  # 问道、问：
                r'答道?[：:]',  # 答道、答：
            ],
            'description': [
                r'描述|形容|外观|样子|模样',
                r'看起来|显得|似乎|仿佛',
                r'环境|景色|风景|场景',
            ],
            'action': [
                r'走|跑|跳|坐|站|躺|蹲',
                r'拿|放|抓|握|推|拉|打|击',
                r'动作|行为|举动',
            ],
            'psychology': [
                r'想|思考|考虑|琢磨|思索',
                r'感觉|觉得|认为|以为',
                r'心里|内心|心中|脑海',
            ],
            'emotion': [
                r'高兴|开心|快乐|兴奋|激动',
                r'悲伤|难过|伤心|痛苦|沮丧',
                r'愤怒|生气|恼火|暴怒|愤慨',
                r'恐惧|害怕|担心|紧张|焦虑',
            ]
        }
    
    async def build_chapter_index(self, project_id: str, chapter_id: str) -> ChapterIndex:
        """构建章节索引"""
        try:
            self.logger.info(f"构建章节索引: {chapter_id}")
            
            # 获取章节基本信息
            chapter = chapter_service.get_chapter(project_id, chapter_id)
            if not chapter:
                raise ValueError(f"章节不存在: {chapter_id}")
            
            # 提取章节号
            chapter_number = self._extract_chapter_number(chapter.title)
            
            # 分析内容类型
            content_types = self._analyze_content_types(chapter.content)
            
            # 从AI记忆中获取分析结果
            characters, scenes, plot_points, themes, emotional_tone = await self._extract_from_memory(
                project_id, chapter_id
            )
            
            # 创建索引
            index = ChapterIndex(
                chapter_id=chapter_id,
                project_id=project_id,
                title=chapter.title,
                chapter_number=chapter_number,
                word_count=len(chapter.content),
                content_types=content_types,
                main_characters=characters,
                scenes=scenes,
                plot_points=plot_points,
                themes=themes,
                emotional_tone=emotional_tone,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                vectorized=chapter.vectorized
            )
            
            # 保存索引
            await self._save_chapter_index(index)
            
            self.logger.info(f"章节索引构建完成: {chapter_id}")
            return index
            
        except Exception as e:
            self.logger.error(f"构建章节索引失败: {e}")
            raise
    
    def _extract_chapter_number(self, title: str) -> Optional[int]:
        """从标题中提取章节号"""
        try:
            # 匹配各种章节号格式
            patterns = [
                r'第(\d+)章',
                r'第(\d+)回',
                r'Chapter\s*(\d+)',
                r'chapter\s*(\d+)',
                r'(\d+)\.',
                r'(\d+)、',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, title, re.IGNORECASE)
                if match:
                    return int(match.group(1))
            
            return None
            
        except Exception as e:
            self.logger.error(f"提取章节号失败: {e}")
            return None
    
    def _analyze_content_types(self, content: str) -> List[str]:
        """分析内容类型"""
        try:
            content_types = []
            
            for content_type, patterns in self.content_type_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        if content_type not in content_types:
                            content_types.append(content_type)
                        break
            
            return content_types
            
        except Exception as e:
            self.logger.error(f"分析内容类型失败: {e}")
            return []
    
    async def _extract_from_memory(self, project_id: str, chapter_id: str) -> Tuple[List[str], List[str], List[str], List[str], str]:
        """从AI记忆中提取信息"""
        try:
            characters = []
            scenes = []
            plot_points = []
            themes = []
            emotional_tone = ""
            
            # 获取章节相关的记忆
            memories = await self.ai_memory.search_memories(
                project_id=project_id,
                query="",
                memory_types=[MemoryType.CHARACTER, MemoryType.SCENE, MemoryType.PLOT, MemoryType.WORLD],
                chapter_id=chapter_id,
                limit=50
            )
            
            for memory in memories:
                if memory.type == MemoryType.CHARACTER:
                    # 提取角色名称
                    char_names = self._extract_character_names(memory.content)
                    characters.extend(char_names)
                
                elif memory.type == MemoryType.SCENE:
                    # 提取场景信息
                    scene_names = self._extract_scene_names(memory.content)
                    scenes.extend(scene_names)
                
                elif memory.type == MemoryType.PLOT:
                    # 提取情节要点
                    plot_info = self._extract_plot_points(memory.content)
                    plot_points.extend(plot_info)
                
                elif memory.type == MemoryType.WORLD:
                    # 提取主题信息
                    theme_info = self._extract_themes(memory.content)
                    themes.extend(theme_info)
            
            # 去重
            characters = list(set(characters))
            scenes = list(set(scenes))
            plot_points = list(set(plot_points))
            themes = list(set(themes))
            
            # 分析情感基调
            emotional_tone = await self._analyze_emotional_tone(project_id, chapter_id)
            
            return characters, scenes, plot_points, themes, emotional_tone
            
        except Exception as e:
            self.logger.error(f"从记忆中提取信息失败: {e}")
            return [], [], [], [], ""
    
    def _extract_character_names(self, content: str) -> List[str]:
        """从内容中提取角色名称"""
        try:
            characters = []
            
            # 尝试解析JSON格式的角色信息
            try:
                data = json.loads(content)
                if 'characters' in data:
                    for char in data['characters']:
                        if isinstance(char, dict) and 'name' in char:
                            characters.append(char['name'])
                        elif isinstance(char, str):
                            characters.append(char)
            except json.JSONDecodeError:
                # 如果不是JSON，使用正则表达式提取
                char_pattern = r'角色[：:]?([^，。！？\s]+)'
                matches = re.findall(char_pattern, content)
                characters.extend(matches)
            
            return characters
            
        except Exception as e:
            self.logger.error(f"提取角色名称失败: {e}")
            return []

    def _extract_scene_names(self, content: str) -> List[str]:
        """从内容中提取场景名称"""
        try:
            scenes = []

            try:
                data = json.loads(content)
                if 'scenes' in data:
                    for scene in data['scenes']:
                        if isinstance(scene, dict) and 'location' in scene:
                            scenes.append(scene['location'])
                        elif isinstance(scene, str):
                            scenes.append(scene)
            except json.JSONDecodeError:
                scene_pattern = r'场景[：:]?([^，。！？\s]+)'
                matches = re.findall(scene_pattern, content)
                scenes.extend(matches)

            return scenes

        except Exception as e:
            self.logger.error(f"提取场景名称失败: {e}")
            return []

    def _extract_plot_points(self, content: str) -> List[str]:
        """从内容中提取情节要点"""
        try:
            plot_points = []

            try:
                data = json.loads(content)
                if 'main_events' in data:
                    for event in data['main_events']:
                        if isinstance(event, dict) and 'event' in event:
                            plot_points.append(event['event'])
                        elif isinstance(event, str):
                            plot_points.append(event)
            except json.JSONDecodeError:
                # 提取关键情节词汇
                plot_keywords = ['冲突', '转折', '高潮', '结局', '发展', '事件']
                for keyword in plot_keywords:
                    if keyword in content:
                        plot_points.append(keyword)

            return plot_points

        except Exception as e:
            self.logger.error(f"提取情节要点失败: {e}")
            return []

    def _extract_themes(self, content: str) -> List[str]:
        """从内容中提取主题"""
        try:
            themes = []

            try:
                data = json.loads(content)
                if 'main_themes' in data:
                    for theme in data['main_themes']:
                        if isinstance(theme, dict) and 'theme' in theme:
                            themes.append(theme['theme'])
                        elif isinstance(theme, str):
                            themes.append(theme)
            except json.JSONDecodeError:
                # 提取主题关键词
                theme_keywords = ['爱情', '友情', '成长', '冒险', '正义', '复仇', '救赎', '牺牲']
                for keyword in theme_keywords:
                    if keyword in content:
                        themes.append(keyword)

            return themes

        except Exception as e:
            self.logger.error(f"提取主题失败: {e}")
            return []

    async def _analyze_emotional_tone(self, project_id: str, chapter_id: str) -> str:
        """分析情感基调"""
        try:
            # 从情感分析记忆中获取信息
            memories = await self.ai_memory.search_memories(
                project_id=project_id,
                query="情感 基调 氛围",
                chapter_id=chapter_id,
                limit=5
            )

            for memory in memories:
                try:
                    data = json.loads(memory.content)
                    if 'overall_tone' in data:
                        return data['overall_tone']
                except json.JSONDecodeError:
                    continue

            return "中性"

        except Exception as e:
            self.logger.error(f"分析情感基调失败: {e}")
            return "中性"

    async def _save_chapter_index(self, index: ChapterIndex):
        """保存章节索引"""
        try:
            # 将索引保存为特殊的记忆条目
            from app.models.memory import MemoryEntry, MemoryType
            import uuid

            memory_entry = MemoryEntry(
                id=str(uuid.uuid4()),
                project_id=index.project_id,
                chapter_id=index.chapter_id,
                type=MemoryType.WORLD,  # 使用WORLD类型存储索引
                title=f"章节索引 - {index.title}",
                content=json.dumps(asdict(index), ensure_ascii=False, indent=2, default=str),
                summary=f"第{index.chapter_number}章索引，包含{len(index.main_characters)}个角色，{len(index.scenes)}个场景",
                tags=['chapter_index', 'metadata'],
                metadata={
                    'index_type': 'chapter',
                    'chapter_number': index.chapter_number,
                    'word_count': index.word_count
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

            await self.ai_memory.add_memory(memory_entry)

        except Exception as e:
            self.logger.error(f"保存章节索引失败: {e}")

    async def search_chapters(self, query: ChapterSearchQuery) -> List[ChapterSearchResult]:
        """搜索章节"""
        try:
            self.logger.info(f"搜索章节: {query.query_text}")

            results = []

            if query.search_type == "semantic":
                results = await self._semantic_search(query)
            elif query.search_type == "keyword":
                results = await self._keyword_search(query)
            elif query.search_type == "hybrid":
                semantic_results = await self._semantic_search(query)
                keyword_results = await self._keyword_search(query)
                results = self._merge_search_results(semantic_results, keyword_results)

            # 应用过滤条件
            results = self._apply_filters(results, query)

            # 排序和限制结果数量
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            results = results[:query.limit]

            self.logger.info(f"搜索完成，返回 {len(results)} 个结果")
            return results

        except Exception as e:
            self.logger.error(f"搜索章节失败: {e}")
            return []

    async def _semantic_search(self, query: ChapterSearchQuery) -> List[ChapterSearchResult]:
        """语义搜索"""
        try:
            # 使用AI记忆系统进行语义搜索
            memories = await self.ai_memory.search_memories(
                project_id=query.project_id,
                query=query.query_text,
                limit=query.limit * 2,  # 获取更多结果用于后续过滤
                similarity_threshold=query.similarity_threshold
            )

            results = []
            for memory in memories:
                if 'chapter_index' in memory.tags:
                    try:
                        index_data = json.loads(memory.content)
                        index = ChapterIndex(**index_data)

                        result = ChapterSearchResult(
                            chapter_index=index,
                            relevance_score=0.8,  # 默认相关性分数
                            match_reasons=['语义匹配'],
                            content_snippet=memory.summary
                        )
                        results.append(result)

                    except (json.JSONDecodeError, TypeError) as e:
                        self.logger.error(f"解析章节索引失败: {e}")
                        continue

            return results

        except Exception as e:
            self.logger.error(f"语义搜索失败: {e}")
            return []

    async def _keyword_search(self, query: ChapterSearchQuery) -> List[ChapterSearchResult]:
        """关键词搜索"""
        try:
            # 获取所有章节索引
            memories = await self.ai_memory.search_memories(
                project_id=query.project_id,
                query="",
                tags=['chapter_index'],
                limit=100
            )

            results = []
            keywords = query.query_text.lower().split()

            for memory in memories:
                try:
                    index_data = json.loads(memory.content)
                    index = ChapterIndex(**index_data)

                    # 计算关键词匹配分数
                    score, reasons = self._calculate_keyword_score(index, keywords)

                    if score > 0:
                        result = ChapterSearchResult(
                            chapter_index=index,
                            relevance_score=score,
                            match_reasons=reasons,
                            content_snippet=f"第{index.chapter_number}章: {index.title}"
                        )
                        results.append(result)

                except (json.JSONDecodeError, TypeError) as e:
                    self.logger.error(f"解析章节索引失败: {e}")
                    continue

            return results

        except Exception as e:
            self.logger.error(f"关键词搜索失败: {e}")
            return []

    def _calculate_keyword_score(self, index: ChapterIndex, keywords: List[str]) -> Tuple[float, List[str]]:
        """计算关键词匹配分数"""
        try:
            score = 0.0
            reasons = []

            # 检查标题匹配
            title_lower = index.title.lower()
            for keyword in keywords:
                if keyword in title_lower:
                    score += 0.3
                    reasons.append(f"标题匹配: {keyword}")

            # 检查角色匹配
            for char in index.main_characters:
                char_lower = char.lower()
                for keyword in keywords:
                    if keyword in char_lower:
                        score += 0.2
                        reasons.append(f"角色匹配: {char}")

            # 检查场景匹配
            for scene in index.scenes:
                scene_lower = scene.lower()
                for keyword in keywords:
                    if keyword in scene_lower:
                        score += 0.2
                        reasons.append(f"场景匹配: {scene}")

            # 检查主题匹配
            for theme in index.themes:
                theme_lower = theme.lower()
                for keyword in keywords:
                    if keyword in theme_lower:
                        score += 0.15
                        reasons.append(f"主题匹配: {theme}")

            # 检查情感基调匹配
            if index.emotional_tone:
                tone_lower = index.emotional_tone.lower()
                for keyword in keywords:
                    if keyword in tone_lower:
                        score += 0.1
                        reasons.append(f"情感匹配: {index.emotional_tone}")

            return score, reasons

        except Exception as e:
            self.logger.error(f"计算关键词分数失败: {e}")
            return 0.0, []

    def _merge_search_results(self, semantic_results: List[ChapterSearchResult],
                            keyword_results: List[ChapterSearchResult]) -> List[ChapterSearchResult]:
        """合并搜索结果"""
        try:
            merged = {}

            # 添加语义搜索结果
            for result in semantic_results:
                chapter_id = result.chapter_index.chapter_id
                merged[chapter_id] = result
                merged[chapter_id].relevance_score *= 0.6  # 语义搜索权重

            # 合并关键词搜索结果
            for result in keyword_results:
                chapter_id = result.chapter_index.chapter_id
                if chapter_id in merged:
                    # 合并分数和原因
                    merged[chapter_id].relevance_score += result.relevance_score * 0.4
                    merged[chapter_id].match_reasons.extend(result.match_reasons)
                else:
                    result.relevance_score *= 0.4  # 关键词搜索权重
                    merged[chapter_id] = result

            return list(merged.values())

        except Exception as e:
            self.logger.error(f"合并搜索结果失败: {e}")
            return semantic_results + keyword_results

    def _apply_filters(self, results: List[ChapterSearchResult],
                      query: ChapterSearchQuery) -> List[ChapterSearchResult]:
        """应用过滤条件"""
        try:
            filtered = []

            for result in results:
                index = result.chapter_index

                # 章节号过滤
                if query.chapter_numbers and index.chapter_number not in query.chapter_numbers:
                    continue

                # 角色过滤
                if query.characters:
                    if not any(char in index.main_characters for char in query.characters):
                        continue

                # 场景过滤
                if query.scenes:
                    if not any(scene in index.scenes for scene in query.scenes):
                        continue

                # 内容类型过滤
                if query.content_types:
                    if not any(ct in index.content_types for ct in query.content_types):
                        continue

                # 主题过滤
                if query.themes:
                    if not any(theme in index.themes for theme in query.themes):
                        continue

                # 情感基调过滤
                if query.emotional_tone and query.emotional_tone != index.emotional_tone:
                    continue

                filtered.append(result)

            return filtered

        except Exception as e:
            self.logger.error(f"应用过滤条件失败: {e}")
            return results

    async def get_chapter_by_number(self, project_id: str, chapter_number: int) -> Optional[ChapterIndex]:
        """根据章节号获取章节索引"""
        try:
            query = ChapterSearchQuery(
                project_id=project_id,
                chapter_numbers=[chapter_number],
                search_type="keyword",
                limit=1
            )

            results = await self.search_chapters(query)
            return results[0].chapter_index if results else None

        except Exception as e:
            self.logger.error(f"根据章节号获取章节失败: {e}")
            return None

    async def get_chapters_by_character(self, project_id: str, character_name: str) -> List[ChapterIndex]:
        """获取包含指定角色的章节"""
        try:
            query = ChapterSearchQuery(
                project_id=project_id,
                characters=[character_name],
                search_type="keyword",
                limit=50
            )

            results = await self.search_chapters(query)
            return [result.chapter_index for result in results]

        except Exception as e:
            self.logger.error(f"根据角色获取章节失败: {e}")
            return []


# 全局实例
chapter_index_service = ChapterIndexService()


def get_chapter_index_service() -> ChapterIndexService:
    """获取章节索引服务实例"""
    return chapter_index_service
