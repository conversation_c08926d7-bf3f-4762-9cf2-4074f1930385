"""
鲁棒的JSON解析器
专门处理LLM模型（特别是Ollama）可能产生的不完整或格式错误的JSON
"""

import json
import re
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class RobustJSONParser:
    """鲁棒的JSON解析器"""
    
    def __init__(self):
        self.logger = logger
    
    def parse_json_from_response(self, response: str) -> Optional[Dict[str, Any]]:
        """
        从AI响应中解析JSON，支持多种修复策略
        
        Args:
            response: AI模型的原始响应
            
        Returns:
            解析后的JSON字典，如果解析失败返回None
        """
        if not response or not response.strip():
            self.logger.warning("响应为空")
            return None
        
        # 策略1: 直接解析
        try:
            return json.loads(response.strip())
        except json.JSONDecodeError:
            pass
        
        # 策略2: 提取JSON部分
        json_str = self._extract_json_block(response)
        if json_str:
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                pass
        
        # 策略3: 修复常见JSON错误
        fixed_json = self._fix_common_json_errors(json_str or response)
        if fixed_json:
            try:
                return json.loads(fixed_json)
            except json.JSONDecodeError:
                pass
        
        # 策略4: 逐步修复JSON
        repaired_json = self._repair_json_step_by_step(json_str or response)
        if repaired_json:
            try:
                return json.loads(repaired_json)
            except json.JSONDecodeError:
                pass
        
        # 策略5: 提取部分有效数据
        partial_data = self._extract_partial_data(response)
        if partial_data:
            return partial_data
        
        self.logger.error(f"所有JSON解析策略都失败了，原始响应: {response[:200]}...")
        return None
    
    def _extract_json_block(self, response: str) -> Optional[str]:
        """提取JSON代码块"""
        # 查找```json代码块
        json_block_pattern = r'```json\s*(.*?)\s*```'
        match = re.search(json_block_pattern, response, re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()
        
        # 查找普通代码块
        code_block_pattern = r'```\s*(.*?)\s*```'
        match = re.search(code_block_pattern, response, re.DOTALL)
        if match:
            content = match.group(1).strip()
            if content.startswith('{') and content.endswith('}'):
                return content
        
        # 查找第一个完整的JSON对象
        start_idx = response.find('{')
        if start_idx == -1:
            return None
        
        brace_count = 0
        for i, char in enumerate(response[start_idx:], start_idx):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    return response[start_idx:i + 1]
        
        return None
    
    def _fix_common_json_errors(self, json_str: str) -> Optional[str]:
        """修复常见的JSON错误"""
        if not json_str:
            return None
        
        # 移除注释
        json_str = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)
        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
        
        # 修复尾随逗号
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
        
        # 修复单引号
        json_str = re.sub(r"'([^']*)':", r'"\1":', json_str)
        json_str = re.sub(r":\s*'([^']*)'", r': "\1"', json_str)
        
        # 修复未引用的键
        json_str = re.sub(r'(\w+):', r'"\1":', json_str)
        
        # 修复多余的逗号
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)
        
        # 确保字符串值被正确引用
        json_str = re.sub(r':\s*([^",\[\]{}]+)(?=\s*[,}])', r': "\1"', json_str)
        
        return json_str.strip()
    
    def _repair_json_step_by_step(self, json_str: str) -> Optional[str]:
        """逐步修复JSON"""
        if not json_str:
            return None
        
        # 确保以{开始，以}结束
        json_str = json_str.strip()
        if not json_str.startswith('{'):
            start_idx = json_str.find('{')
            if start_idx != -1:
                json_str = json_str[start_idx:]
            else:
                return None
        
        if not json_str.endswith('}'):
            end_idx = json_str.rfind('}')
            if end_idx != -1:
                json_str = json_str[:end_idx + 1]
            else:
                # 尝试补全缺失的}
                open_braces = json_str.count('{')
                close_braces = json_str.count('}')
                if open_braces > close_braces:
                    json_str += '}' * (open_braces - close_braces)
        
        # 修复不完整的数组
        json_str = re.sub(r'\[\s*$', '[]', json_str)
        json_str = re.sub(r',\s*$', '', json_str)
        
        return json_str
    
    def _extract_partial_data(self, response: str) -> Optional[Dict[str, Any]]:
        """从响应中提取部分有效数据"""
        try:
            # 尝试提取角色信息
            characters = []
            
            # 查找角色名称模式
            name_patterns = [
                r'"name":\s*"([^"]+)"',
                r'name:\s*"([^"]+)"',
                r'姓名[：:]\s*([^\n,]+)',
                r'角色[：:]\s*([^\n,]+)'
            ]
            
            names = []
            for pattern in name_patterns:
                matches = re.findall(pattern, response, re.IGNORECASE)
                names.extend(matches)
            
            # 为每个找到的名称创建基础角色数据
            for name in set(names):
                if name.strip():
                    characters.append({
                        "name": name.strip(),
                        "description": f"从文本中提取的角色: {name}",
                        "identity": "未知",
                        "personality_tags": [],
                        "appearance": "",
                        "background": "",
                        "current_status": "活跃",
                        "goals": [],
                        "abilities": [],
                        "weaknesses": []
                    })
            
            if characters:
                return {"characters": characters}
            
            return None
            
        except Exception as e:
            self.logger.error(f"提取部分数据失败: {e}")
            return None


# 全局实例
robust_json_parser = RobustJSONParser()


def get_robust_json_parser() -> RobustJSONParser:
    """获取鲁棒JSON解析器实例"""
    return robust_json_parser


def parse_llm_json_response(response: str) -> Optional[Dict[str, Any]]:
    """
    便捷函数：解析LLM的JSON响应
    
    Args:
        response: LLM的原始响应
        
    Returns:
        解析后的JSON字典，如果解析失败返回None
    """
    return robust_json_parser.parse_json_from_response(response)
