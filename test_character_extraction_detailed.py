#!/usr/bin/env python3
"""
详细测试角色提取功能
验证LLM是否正确提取角色信息
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.enhanced_chapter_vectorizer import get_enhanced_chapter_vectorizer
from app.services.character_storage_service import character_storage_service

# 测试文本
TEST_TEXT = """
第一章：初入江湖

李明是一个年轻勇敢的剑客，自幼在华山派学艺。他身材高大，眼神坚毅，擅长华山剑法和轻功。他的师父张无忌是一位德高望重的武林高手，曾经是明教教主，精通九阳神功和乾坤大挪移。

"师父，我什么时候能下山？"李明恭敬地问道。

"当你能战胜王大龙时。"张无忌回答。王大龙是华山派的大师兄，武功高强，性格傲慢，擅长华山剑法中的独孤九剑。

李明苦练了三年，终于在一个月圆之夜挑战王大龙。两人在华山之巅展开了激烈的决斗。

"李明，你的剑法确实有所进步，但还不足以战胜我！"王大龙冷笑道，手中长剑如银蛇般舞动。

李明深吸一口气，施展出师父传授的绝技"华山剑法"。剑光如虹，直指王大龙的要害。

经过一番激战，李明凭借着坚韧的意志和精湛的剑法，最终击败了王大龙。

"好！李明，你已经可以下山了。"张无忌欣慰地说道。

第二天，李明告别了师父和同门，踏上了闯荡江湖的道路。

在下山的路上，李明遇到了一群山贼正在劫掠商队。商队中有一位美丽的女子林雪儿正在拼命抵抗。林雪儿是江南林家的千金小姐，精通琴棋书画，虽然不会武功，但聪明机智。

"住手！"李明大喝一声，拔剑冲向山贼。

山贼头目黑风是一个满脸横肉的大汉，擅长刀法，在江湖上恶名昭著。见李明年轻，不禁轻视道："小子，识相的就滚开，否则别怪我们不客气！"

李明冷笑一声："就凭你们这些乌合之众，也敢在我面前放肆？"

一场恶战即将开始...

最终，李明凭借着高超的武功击败了所有山贼，解救了商队。

"多谢少侠相救！"林雪儿感激地说道。

"举手之劳，不足挂齿。"李明谦逊地回答。

从此，李明在江湖上开始了他的传奇人生...
"""

async def test_direct_vectorization():
    """直接测试向量化服务"""
    print("🔧 直接测试向量化服务")
    print("=" * 50)
    
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        
        print("🔄 开始向量化...")
        result = await vectorizer.vectorize_chapter(
            project_id="test_project",
            chapter_id="test_chapter",
            content=TEST_TEXT,
            title="测试章节",
            use_llm=True
        )
        
        print(f"✅ 向量化完成")
        print(f"📊 成功状态: {result.success}")
        print(f"📊 处理时间: {result.processing_time:.2f}秒")
        
        if result.structured_data:
            characters = result.structured_data.get('characters', [])
            scenes = result.structured_data.get('scenes', [])
            dialogues = result.structured_data.get('dialogues', [])
            themes = result.structured_data.get('themes', [])
            
            print(f"\n📊 结构化数据:")
            print(f"   角色: {len(characters)}个")
            print(f"   场景: {len(scenes)}个")
            print(f"   对话: {len(dialogues)}个")
            print(f"   主题: {len(themes)}个")
            
            # 显示角色详情
            if characters:
                print(f"\n👥 提取的角色:")
                for i, char in enumerate(characters, 1):
                    print(f"  {i}. {char.get('name', '未知')}")
                    print(f"     性别: {char.get('gender', '未知')}")
                    print(f"     年龄: {char.get('age', '未知')}")
                    print(f"     职业: {char.get('occupation', '未知')}")
                    print(f"     性格: {', '.join(char.get('personality_traits', []))}")
                    print(f"     能力: {', '.join(char.get('abilities', []))}")
                    print(f"     特长: {', '.join(char.get('specialties', []))}")
                    print(f"     技能: {', '.join(char.get('skills', []))}")
                    print(f"     重要性: {char.get('importance', 'minor')}")
                    print(f"     描述: {char.get('description', '无')}")
                    print()
            else:
                print(f"\n⚠️ 没有提取到角色信息")
        
        # 检查角色存储
        print(f"\n💾 检查角色存储...")
        stored_characters = character_storage_service.get_project_characters("test_project")
        print(f"📊 存储的角色数量: {len(stored_characters)}")
        
        if stored_characters:
            print(f"\n💾 存储的角色:")
            for i, char in enumerate(stored_characters, 1):
                print(f"  {i}. {char.get('name', '未知')}")
                print(f"     ID: {char.get('id', '未知')}")
                print(f"     项目ID: {char.get('project_id', '未知')}")
                print(f"     章节ID: {char.get('chapter_id', '未知')}")
                print(f"     出现次数: {len(char.get('appearances', []))}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ 向量化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_character_storage():
    """测试角色存储功能"""
    print(f"\n💾 测试角色存储功能")
    print("=" * 50)
    
    try:
        # 创建测试角色数据
        from app.services.text_structure_analyzer import Character
        
        test_characters = [
            Character(
                name="李明",
                gender="male",
                age="年轻",
                personality_traits=["勇敢", "坚毅", "好学"],
                abilities=["剑法", "武功"],
                specialties=["华山剑法"],
                skills=["剑术", "轻功"],
                occupation="剑客",
                background="华山派弟子",
                appearance="身材高大，眼神坚毅",
                description="年轻勇敢的剑客，擅长华山剑法",
                relationships=[
                    {"target": "张无忌", "relation": "师徒", "description": "张无忌的弟子"}
                ],
                importance="main",
                first_appearance="当前章节"
            ),
            Character(
                name="张无忌",
                gender="male",
                age="中年",
                personality_traits=["德高望重", "武功高强", "慈祥"],
                abilities=["内功心法", "武林绝学"],
                specialties=["九阳神功", "乾坤大挪移"],
                skills=["内功", "剑法", "拳法"],
                occupation="武林高手",
                background="前明教教主",
                appearance="unknown",
                description="武林中德高望重的高手，精通内功心法",
                relationships=[
                    {"target": "李明", "relation": "师徒", "description": "李明的师父"}
                ],
                importance="secondary",
                first_appearance="当前章节"
            )
        ]
        
        # 保存角色
        saved_characters = character_storage_service.save_characters(
            project_id="test_storage_project",
            chapter_id="test_storage_chapter",
            characters=test_characters
        )
        
        print(f"✅ 保存角色成功: {len(saved_characters)}个")
        
        # 获取角色
        retrieved_characters = character_storage_service.get_project_characters("test_storage_project")
        print(f"✅ 获取角色成功: {len(retrieved_characters)}个")
        
        # 显示角色详情
        for i, char in enumerate(retrieved_characters, 1):
            print(f"\n  {i}. {char.get('name', '未知')}")
            print(f"     性别: {char.get('gender', '未知')}")
            print(f"     职业: {char.get('occupation', '未知')}")
            print(f"     能力: {', '.join(char.get('abilities', []))}")
            print(f"     特长: {', '.join(char.get('specialties', []))}")
            print(f"     重要性: {char.get('importance', 'minor')}")
        
        # 导出JSON
        json_data = character_storage_service.export_characters_json("test_storage_project")
        print(f"\n✅ 导出JSON成功")
        
        # 保存到文件
        with open('test_characters.json', 'w', encoding='utf-8') as f:
            f.write(json_data)
        print(f"💾 测试角色JSON已保存到: test_characters.json")
        
        return True
        
    except Exception as e:
        print(f"❌ 角色存储测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 详细角色提取测试")
    print("🎯 验证LLM角色提取和存储功能")
    
    # 1. 测试直接向量化
    vectorization_ok = await test_direct_vectorization()
    
    # 2. 测试角色存储
    storage_ok = test_character_storage()
    
    print(f"\n" + "=" * 60)
    print("🏆 测试结果总结")
    print("=" * 60)
    
    results = {
        "向量化服务": vectorization_ok,
        "角色存储": storage_ok
    }
    
    for test_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    overall_success = all(results.values())
    
    if overall_success:
        print(f"\n🎉 所有测试通过！")
        print(f"\n🌟 功能验证:")
        print(f"   ✅ LLM能够正确提取角色信息")
        print(f"   ✅ 角色信息包含详细的属性")
        print(f"   ✅ 角色数据正确存储到数据库")
        print(f"   ✅ JSON导出功能正常工作")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试")
    
    return overall_success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
