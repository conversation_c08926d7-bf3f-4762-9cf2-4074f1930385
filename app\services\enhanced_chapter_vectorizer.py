
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

from app.services.text_structure_analyzer import get_text_structure_analyzer, StructuredChapter
from app.services.llm_text_analyzer import get_llm_text_analyzer
from app.services.knowledge_graph_manager import get_knowledge_graph_manager
from app.services.multi_level_vectorizer import get_multi_level_vectorizer
from app.services.intelligent_query_engine import get_intelligent_query_engine
from app.services.ai_memory_service import get_ai_memory_service
from app.services.chapter_service import chapter_service
from app.services.character_storage_service import character_storage_service


@dataclass
class VectorizationResult:
    """向量化结果"""
    chapter_id: str
    project_id: str
    success: bool
    processing_time: float
    structured_data: Dict[str, Any] = None
    knowledge_graph_stats: Dict[str, Any] = None
    vector_stats: Dict[str, Any] = None
    error_message: str = ""


@dataclass
class ProjectVectorizationStats:
    """项目向量化统计"""
    project_id: str
    total_chapters: int
    vectorized_chapters: int
    total_entities: int
    total_relationships: int
    total_vectors: int
    last_updated: str
    processing_time: float


@dataclass
class MultiDimensionAnalysisResult:
    """多维度分析结果"""
    chapter_id: str
    project_id: str
    dimensions: Dict[str, Any]  # 各维度分析结果
    success: bool
    processing_time: float
    error_message: str = ""


class EnhancedChapterVectorizer:
    """增强的章节向量化器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.text_analyzer = get_text_structure_analyzer()
        self.llm_analyzer = get_llm_text_analyzer()
        self.kg_manager = get_knowledge_graph_manager()
        self.vectorizer = get_multi_level_vectorizer()
        self.query_engine = get_intelligent_query_engine()
        self.ai_memory = get_ai_memory_service()

        # 多维度分析配置
        self.analysis_dimensions = {
            'character_analysis': {
                'name': '角色分析',
                'description': '提取和分析章节中的角色信息、性格特征、关系变化',
                'prompt_template': self._get_character_analysis_prompt(),
                'enabled': True
            },
            'plot_analysis': {
                'name': '情节分析',
                'description': '分析章节的情节发展、冲突、转折点',
                'prompt_template': self._get_plot_analysis_prompt(),
                'enabled': True
            },
            'scene_analysis': {
                'name': '场景分析',
                'description': '分析章节的场景设定、环境描述、氛围营造',
                'prompt_template': self._get_scene_analysis_prompt(),
                'enabled': True
            },
            'emotion_analysis': {
                'name': '情感分析',
                'description': '分析章节的情感基调、角色情感变化',
                'prompt_template': self._get_emotion_analysis_prompt(),
                'enabled': True
            },
            'theme_analysis': {
                'name': '主题分析',
                'description': '分析章节的主题思想、象征意义',
                'prompt_template': self._get_theme_analysis_prompt(),
                'enabled': True
            },
            'worldbuilding_analysis': {
                'name': '世界观分析',
                'description': '提取章节中的世界观设定、规则、背景信息',
                'prompt_template': self._get_worldbuilding_analysis_prompt(),
                'enabled': True
            }
        }

    def _get_character_analysis_prompt(self) -> str:
        """获取角色分析提示词"""
        return """
请分析以下章节文本中的角色信息，包括：
1. 出现的角色及其基本信息
2. 角色的性格特征和行为模式
3. 角色之间的关系变化
4. 角色的情感状态和心理活动
5. 角色的成长和发展

请以JSON格式输出分析结果：
{
    "characters": [
        {
            "name": "角色名称",
            "role": "主角/配角/次要角色",
            "personality_traits": ["性格特征1", "性格特征2"],
            "emotional_state": "当前情感状态",
            "actions": ["主要行为1", "主要行为2"],
            "dialogue_style": "对话风格描述",
            "relationships": {
                "角色名": "关系描述"
            },
            "development": "角色在本章的发展变化"
        }
    ],
    "relationship_changes": [
        {
            "characters": ["角色A", "角色B"],
            "change_type": "关系变化类型",
            "description": "变化描述"
        }
    ]
}
"""

    def _get_plot_analysis_prompt(self) -> str:
        """获取情节分析提示词"""
        return """
请分析以下章节文本的情节结构，包括：
1. 主要情节线和事件
2. 冲突和矛盾
3. 情节转折点
4. 悬念和伏笔
5. 情节推进方式

请以JSON格式输出分析结果：
{
    "main_events": [
        {
            "event": "事件描述",
            "importance": "高/中/低",
            "type": "开端/发展/高潮/结局",
            "characters_involved": ["相关角色"]
        }
    ],
    "conflicts": [
        {
            "type": "内在冲突/外在冲突/人际冲突",
            "description": "冲突描述",
            "participants": ["参与者"]
        }
    ],
    "turning_points": [
        {
            "description": "转折点描述",
            "impact": "影响描述"
        }
    ],
    "foreshadowing": [
        {
            "element": "伏笔元素",
            "potential_outcome": "可能的结果"
        }
    ],
    "plot_progression": "情节推进方式描述"
}
"""

    def _get_scene_analysis_prompt(self) -> str:
        """获取场景分析提示词"""
        return """
请分析以下章节文本的场景设定，包括：
1. 场景地点和环境
2. 时间设定
3. 氛围和基调
4. 场景功能和作用
5. 环境对情节的影响

请以JSON格式输出分析结果：
{
    "scenes": [
        {
            "location": "地点名称",
            "description": "环境描述",
            "time": "时间设定",
            "atmosphere": "氛围描述",
            "mood": "情绪基调",
            "function": "场景功能",
            "significance": "重要性说明"
        }
    ],
    "environmental_factors": [
        {
            "factor": "环境因素",
            "impact": "对情节的影响"
        }
    ],
    "sensory_details": {
        "visual": ["视觉描述"],
        "auditory": ["听觉描述"],
        "tactile": ["触觉描述"],
        "olfactory": ["嗅觉描述"]
    }
}
"""

    def _get_emotion_analysis_prompt(self) -> str:
        """获取情感分析提示词"""
        return """
请分析以下章节文本的情感层面，包括：
1. 整体情感基调
2. 角色情感变化
3. 情感冲突
4. 情感表达方式
5. 读者情感引导

请以JSON格式输出分析结果：
{
    "overall_tone": "整体情感基调",
    "emotional_arc": "情感发展轨迹",
    "character_emotions": [
        {
            "character": "角色名",
            "emotions": ["情感1", "情感2"],
            "emotional_journey": "情感变化过程",
            "triggers": ["情感触发因素"]
        }
    ],
    "emotional_conflicts": [
        {
            "type": "冲突类型",
            "description": "冲突描述",
            "resolution": "解决方式"
        }
    ],
    "reader_engagement": "读者情感引导策略"
}
"""

    def _get_theme_analysis_prompt(self) -> str:
        """获取主题分析提示词"""
        return """
请分析以下章节文本的主题内容，包括：
1. 主要主题思想
2. 象征意义
3. 价值观体现
4. 社会意义
5. 哲学思考

请以JSON格式输出分析结果：
{
    "main_themes": [
        {
            "theme": "主题名称",
            "description": "主题描述",
            "evidence": ["支撑证据"]
        }
    ],
    "symbols": [
        {
            "symbol": "象征物",
            "meaning": "象征意义",
            "context": "出现语境"
        }
    ],
    "values": [
        {
            "value": "价值观",
            "expression": "表达方式"
        }
    ],
    "philosophical_elements": [
        {
            "concept": "哲学概念",
            "exploration": "探讨方式"
        }
    ]
}
"""

    def _get_worldbuilding_analysis_prompt(self) -> str:
        """获取世界观分析提示词"""
        return """
请分析以下章节文本的世界观设定，包括：
1. 世界规则和设定
2. 社会结构
3. 文化背景
4. 技术水平
5. 历史背景

请以JSON格式输出分析结果：
{
    "world_rules": [
        {
            "rule": "规则描述",
            "scope": "适用范围",
            "implications": "影响和意义"
        }
    ],
    "social_structure": {
        "hierarchy": "社会等级",
        "institutions": ["社会机构"],
        "power_dynamics": "权力关系"
    },
    "cultural_elements": [
        {
            "aspect": "文化方面",
            "description": "具体描述",
            "significance": "重要性"
        }
    ],
    "technology_magic": [
        {
            "system": "技术/魔法系统",
            "description": "系统描述",
            "limitations": "限制条件"
        }
    ],
    "historical_context": [
        {
            "event": "历史事件",
            "impact": "对当前的影响"
        }
    ]
}
"""

    async def analyze_chapter_multi_dimension(self, project_id: str, chapter_id: str,
                                            content: str, title: str = "",
                                            dimensions: List[str] = None) -> MultiDimensionAnalysisResult:
        """多维度分析章节"""
        start_time = datetime.now()

        try:
            self.logger.info(f"开始多维度分析章节: {chapter_id}")

            # 如果未指定维度，使用所有启用的维度
            if dimensions is None:
                dimensions = [dim for dim, config in self.analysis_dimensions.items()
                            if config['enabled']]

            analysis_results = {}

            # 逐个维度进行分析
            for dimension in dimensions:
                if dimension not in self.analysis_dimensions:
                    self.logger.warning(f"未知的分析维度: {dimension}")
                    continue

                self.logger.info(f"分析维度: {self.analysis_dimensions[dimension]['name']}")

                try:
                    # 使用LLM进行维度分析
                    result = await self._analyze_single_dimension(
                        content=content,
                        title=title,
                        dimension=dimension,
                        prompt_template=self.analysis_dimensions[dimension]['prompt_template']
                    )

                    analysis_results[dimension] = result

                    # 保存维度分析结果到向量数据库
                    await self._save_dimension_analysis(
                        project_id=project_id,
                        chapter_id=chapter_id,
                        dimension=dimension,
                        analysis_result=result
                    )

                except Exception as e:
                    self.logger.error(f"维度 {dimension} 分析失败: {e}")
                    analysis_results[dimension] = {
                        'error': str(e),
                        'success': False
                    }

            processing_time = (datetime.now() - start_time).total_seconds()

            return MultiDimensionAnalysisResult(
                chapter_id=chapter_id,
                project_id=project_id,
                dimensions=analysis_results,
                success=True,
                processing_time=processing_time
            )

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"多维度分析失败: {e}")

            return MultiDimensionAnalysisResult(
                chapter_id=chapter_id,
                project_id=project_id,
                dimensions={},
                success=False,
                processing_time=processing_time,
                error_message=str(e)
            )

    async def _analyze_single_dimension(self, content: str, title: str,
                                      dimension: str, prompt_template: str) -> Dict[str, Any]:
        """分析单个维度"""
        try:
            # 构建完整的提示词
            full_prompt = f"""
章节标题: {title}

章节内容:
{content}

{prompt_template}
"""

            # 调用LLM进行分析
            response = await self.llm_analyzer._call_llm_api(full_prompt)

            # 解析JSON响应
            import json
            try:
                result = json.loads(response)
                result['success'] = True
                result['dimension'] = dimension
                return result
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}, 响应: {response}")
                return {
                    'success': False,
                    'error': f'JSON解析失败: {str(e)}',
                    'raw_response': response,
                    'dimension': dimension
                }

        except Exception as e:
            self.logger.error(f"维度分析失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'dimension': dimension
            }

    async def _save_dimension_analysis(self, project_id: str, chapter_id: str,
                                     dimension: str, analysis_result: Dict[str, Any]):
        """保存维度分析结果到向量数据库"""
        try:
            if not analysis_result.get('success', False):
                return

            # 为每个维度创建记忆条目
            from app.models.memory import MemoryType, MemoryEntry
            import uuid

            # 根据维度类型选择记忆类型
            memory_type_mapping = {
                'character_analysis': MemoryType.CHARACTER,
                'plot_analysis': MemoryType.PLOT,
                'scene_analysis': MemoryType.SCENE,
                'emotion_analysis': MemoryType.PLOT,  # 情感分析归类为剧情
                'theme_analysis': MemoryType.WORLD,   # 主题分析归类为世界观
                'worldbuilding_analysis': MemoryType.WORLD
            }

            memory_type = memory_type_mapping.get(dimension, MemoryType.PLOT)

            # 创建记忆条目
            memory_entry = MemoryEntry(
                id=str(uuid.uuid4()),
                project_id=project_id,
                chapter_id=chapter_id,
                type=memory_type,
                title=f"{self.analysis_dimensions[dimension]['name']} - 第{chapter_id}章",
                content=json.dumps(analysis_result, ensure_ascii=False, indent=2),
                summary=self._generate_dimension_summary(dimension, analysis_result),
                tags=[dimension, 'multi_dimension_analysis'],
                metadata={
                    'dimension': dimension,
                    'analysis_type': 'multi_dimension',
                    'chapter_id': chapter_id
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

            # 保存到AI记忆系统
            await self.ai_memory.add_memory(memory_entry)

        except Exception as e:
            self.logger.error(f"保存维度分析结果失败: {e}")

    def _generate_dimension_summary(self, dimension: str, analysis_result: Dict[str, Any]) -> str:
        """生成维度分析摘要"""
        try:
            if dimension == 'character_analysis':
                characters = analysis_result.get('characters', [])
                char_names = [char.get('name', '') for char in characters]
                return f"角色分析: 涉及角色 {', '.join(char_names)}"

            elif dimension == 'plot_analysis':
                events = analysis_result.get('main_events', [])
                event_count = len(events)
                return f"情节分析: 包含 {event_count} 个主要事件"

            elif dimension == 'scene_analysis':
                scenes = analysis_result.get('scenes', [])
                locations = [scene.get('location', '') for scene in scenes]
                return f"场景分析: 涉及场景 {', '.join(locations)}"

            elif dimension == 'emotion_analysis':
                tone = analysis_result.get('overall_tone', '')
                return f"情感分析: 整体基调为 {tone}"

            elif dimension == 'theme_analysis':
                themes = analysis_result.get('main_themes', [])
                theme_names = [theme.get('theme', '') for theme in themes]
                return f"主题分析: 主要主题 {', '.join(theme_names)}"

            elif dimension == 'worldbuilding_analysis':
                rules = analysis_result.get('world_rules', [])
                rule_count = len(rules)
                return f"世界观分析: 包含 {rule_count} 个世界规则"

            else:
                return f"{dimension} 分析结果"

        except Exception as e:
            self.logger.error(f"生成摘要失败: {e}")
            return f"{dimension} 分析结果"
    
    async def vectorize_chapter(self, project_id: str, chapter_id: str,
                              content: str, title: str = "", use_llm: bool = True,
                              enable_multi_dimension: bool = True) -> VectorizationResult:
        """向量化单个章节（支持多维度分析）"""
        start_time = datetime.now()

        try:
            self.logger.info(f"开始增强向量化章节: {chapter_id}")

            # 1. 结构化解析（选择使用大模型或传统方法）
            if use_llm:
                self.logger.info(f"使用大模型进行结构化解析: {chapter_id}")
                structured_chapter = self.llm_analyzer.analyze_chapter_with_llm(
                    chapter_id=chapter_id,
                    title=title,
                    content=content
                )
            else:
                self.logger.info(f"使用传统方法进行结构化解析: {chapter_id}")
                structured_chapter = self.text_analyzer.analyze_chapter(
                    chapter_id=chapter_id,
                    title=title,
                    content=content
                )

            # 2. 多维度分析（新增）
            multi_dimension_result = None
            if enable_multi_dimension and use_llm:
                self.logger.info(f"开始多维度分析: {chapter_id}")
                multi_dimension_result = await self.analyze_chapter_multi_dimension(
                    project_id=project_id,
                    chapter_id=chapter_id,
                    content=content,
                    title=title
                )
                self.logger.info(f"多维度分析完成: {chapter_id}")

            # 3. 保存角色信息到数据库
            if structured_chapter.characters:
                self.logger.info(f"保存 {len(structured_chapter.characters)} 个角色到数据库")
                saved_characters = character_storage_service.save_characters(
                    project_id=project_id,
                    chapter_id=chapter_id,
                    characters=structured_chapter.characters
                )
                self.logger.info(f"成功保存 {len(saved_characters)} 个角色")

            # 4. 知识图谱构建
            kg = self.kg_manager.build_knowledge_graph(
                project_id=project_id,
                structured_chapters=[structured_chapter]
            )

            # 5. 多层次向量化
            vectorized_chapter = self.vectorizer.vectorize_chapter(
                project_id=project_id,
                structured_chapter=structured_chapter
            )

            # 5. 更新章节向量化状态
            processing_time = (datetime.now() - start_time).total_seconds()
            
            chapter_service.update_chapter_vectorization_status(
                project_id=project_id,
                chapter_id=chapter_id,
                memory_count=len(vectorized_chapter.semantic_blocks) + 
                           len(vectorized_chapter.paragraphs) + 
                           len(vectorized_chapter.entities),
                last_vectorized=datetime.now().isoformat()
            )
            
            # 6. 构建结果
            vector_stats = {
                'full_text_vectors': 1,
                'semantic_blocks': len(vectorized_chapter.semantic_blocks),
                'paragraphs': len(vectorized_chapter.paragraphs),
                'entities': len(vectorized_chapter.entities),
                'dialogues': len(vectorized_chapter.dialogues)
            }

            # 添加多维度分析统计
            if multi_dimension_result and multi_dimension_result.success:
                vector_stats['multi_dimension_analysis'] = {
                    'enabled': True,
                    'dimensions_analyzed': len(multi_dimension_result.dimensions),
                    'successful_dimensions': len([d for d in multi_dimension_result.dimensions.values()
                                                if d.get('success', False)]),
                    'analysis_time': multi_dimension_result.processing_time
                }
            else:
                vector_stats['multi_dimension_analysis'] = {
                    'enabled': enable_multi_dimension,
                    'dimensions_analyzed': 0,
                    'successful_dimensions': 0
                }

            result = VectorizationResult(
                chapter_id=chapter_id,
                project_id=project_id,
                success=True,
                processing_time=processing_time,
                structured_data=self.text_analyzer.to_dict(structured_chapter),
                knowledge_graph_stats={
                    'entities': len(kg.entities),
                    'relationships': len(kg.relationships)
                },
                vector_stats=vector_stats
            )
            
            self.logger.info(f"章节向量化完成: {chapter_id}, 耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"章节向量化失败: {e}")
            
            return VectorizationResult(
                chapter_id=chapter_id,
                project_id=project_id,
                success=False,
                processing_time=processing_time,
                error_message=str(e)
            )
    
    async def vectorize_project(self, project_id: str, 
                              force_refresh: bool = False) -> ProjectVectorizationStats:
        """向量化整个项目"""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始向量化项目: {project_id}")
            
            # 获取项目所有章节
            chapters = chapter_service.get_chapters(project_id)
            
            vectorized_count = 0
            all_structured_chapters = []
            
            # 处理每个章节
            for chapter_item in chapters:
                chapter_id = chapter_item.id
                
                # 检查是否需要重新向量化
                if not force_refresh and chapter_item.vectorized:
                    vectorized_count += 1
                    continue
                
                # 获取章节内容
                chapter = chapter_service.get_chapter(project_id, chapter_id)
                if not chapter:
                    continue
                
                # 向量化章节
                result = await self.vectorize_chapter(
                    project_id=project_id,
                    chapter_id=chapter_id,
                    content=chapter.content,
                    title=chapter.title
                )
                
                if result.success:
                    vectorized_count += 1
                    
                    # 收集结构化数据用于全局知识图谱
                    if result.structured_data:
                        structured_chapter = StructuredChapter(**result.structured_data)
                        all_structured_chapters.append(structured_chapter)
            
            # 构建全局知识图谱
            if all_structured_chapters:
                global_kg = self.kg_manager.build_knowledge_graph(
                    project_id=project_id,
                    structured_chapters=all_structured_chapters
                )
                
                total_entities = len(global_kg.entities)
                total_relationships = len(global_kg.relationships)
            else:
                total_entities = 0
                total_relationships = 0
            
            # 获取向量统计
            memory_stats = self.ai_memory.get_enhanced_memory_stats(project_id)
            total_vectors = memory_stats.get('total_memories', 0)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            stats = ProjectVectorizationStats(
                project_id=project_id,
                total_chapters=len(chapters),
                vectorized_chapters=vectorized_count,
                total_entities=total_entities,
                total_relationships=total_relationships,
                total_vectors=total_vectors,
                last_updated=datetime.now().isoformat(),
                processing_time=processing_time
            )
            
            self.logger.info(f"项目向量化完成: {project_id}, 耗时: {processing_time:.2f}秒")
            return stats
            
        except Exception as e:
            self.logger.error(f"项目向量化失败: {e}")
            raise
    
    def get_vectorization_status(self, project_id: str) -> Dict[str, Any]:
        """获取向量化状态"""
        try:
            # 获取章节状态
            chapters = chapter_service.get_chapters(project_id)
            
            vectorized_chapters = [c for c in chapters if c.vectorized]
            total_memory_count = sum(c.memory_count for c in vectorized_chapters)
            
            # 获取知识图谱统计
            kg_stats = self.kg_manager.get_knowledge_graph_stats(project_id)
            
            # 获取记忆统计
            memory_stats = self.ai_memory.get_enhanced_memory_stats(project_id)
            
            return {
                'project_id': project_id,
                'total_chapters': len(chapters),
                'vectorized_chapters': len(vectorized_chapters),
                'vectorization_rate': len(vectorized_chapters) / len(chapters) if chapters else 0,
                'total_memories': total_memory_count,
                'knowledge_graph': kg_stats,
                'memory_stats': memory_stats,
                'last_updated': max([c.last_vectorized for c in vectorized_chapters if c.last_vectorized], default=None)
            }
            
        except Exception as e:
            self.logger.error(f"获取向量化状态失败: {e}")
            return {}
    
    def search_enhanced(self, project_id: str, query: str, 
                       search_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """增强搜索"""
        try:
            if search_options is None:
                search_options = {}
            
            # 使用智能查询引擎
            result = self.query_engine.query(
                project_id=project_id,
                query_text=query
            )
            
            return {
                'query': query,
                'intent': asdict(result.intent),
                'vector_results': result.vector_results,
                'graph_results': result.graph_results,
                'synthesized_answer': result.synthesized_answer,
                'confidence': result.confidence,
                'sources': result.sources,
                'reasoning_steps': result.reasoning_steps
            }
            
        except Exception as e:
            self.logger.error(f"增强搜索失败: {e}")
            return {
                'query': query,
                'error': str(e),
                'confidence': 0.0
            }
    
    def get_query_suggestions(self, project_id: str, partial_query: str = "") -> List[str]:
        """获取查询建议"""
        try:
            return self.query_engine.get_query_suggestions(project_id, partial_query)
        except Exception as e:
            self.logger.error(f"获取查询建议失败: {e}")
            return []
    
    def analyze_chapter_structure(self, content: str, use_llm: bool = True) -> Dict[str, Any]:
        """分析章节结构（不保存）"""
        try:
            if use_llm:
                structured_chapter = self.llm_analyzer.analyze_chapter_with_llm(
                    chapter_id="temp",
                    title="临时分析",
                    content=content
                )
            else:
                structured_chapter = self.text_analyzer.analyze_chapter(
                    chapter_id="temp",
                    title="临时分析",
                    content=content
                )

            return self.text_analyzer.to_dict(structured_chapter)

        except Exception as e:
            self.logger.error(f"章节结构分析失败: {e}")
            return {}
    
    def get_project_insights(self, project_id: str) -> Dict[str, Any]:
        """获取项目洞察"""
        try:
            # 获取向量化状态
            status = self.get_vectorization_status(project_id)
            
            # 获取知识图谱统计
            kg_stats = self.kg_manager.get_knowledge_graph_stats(project_id)
            
            # 分析项目特征
            insights = {
                'vectorization_status': status,
                'knowledge_graph_analysis': kg_stats,
                'content_analysis': {
                    'total_entities': kg_stats.get('total_entities', 0),
                    'entity_diversity': len(kg_stats.get('entities', {})),
                    'relationship_complexity': len(kg_stats.get('relationships', {})),
                    'network_density': self._calculate_network_density(kg_stats)
                },
                'recommendations': self._generate_recommendations(status, kg_stats)
            }
            
            return insights
            
        except Exception as e:
            self.logger.error(f"获取项目洞察失败: {e}")
            return {}
    
    def _calculate_network_density(self, kg_stats: Dict[str, Any]) -> float:
        """计算网络密度"""
        total_entities = kg_stats.get('total_entities', 0)
        total_relationships = kg_stats.get('total_relationships', 0)
        
        if total_entities <= 1:
            return 0.0
        
        max_possible_relationships = total_entities * (total_entities - 1) / 2
        return total_relationships / max_possible_relationships if max_possible_relationships > 0 else 0.0
    
    def _generate_recommendations(self, status: Dict[str, Any], 
                                kg_stats: Dict[str, Any]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 向量化建议
        vectorization_rate = status.get('vectorization_rate', 0)
        if vectorization_rate < 0.5:
            recommendations.append("建议完成更多章节的向量化以提高搜索效果")
        
        # 知识图谱建议
        total_entities = kg_stats.get('total_entities', 0)
        if total_entities < 10:
            recommendations.append("建议丰富角色和场景描述以构建更完整的知识图谱")
        
        # 关系网络建议
        network_density = self._calculate_network_density(kg_stats)
        if network_density < 0.1:
            recommendations.append("建议增加角色间的互动以丰富关系网络")
        
        return recommendations


# 全局实例
enhanced_chapter_vectorizer = EnhancedChapterVectorizer()


def get_enhanced_chapter_vectorizer() -> EnhancedChapterVectorizer:
    """获取增强章节向量化器实例"""
    return enhanced_chapter_vectorizer
