"""
多模型向量化服务
支持使用不同的Ollama模型进行内容向量化和角色提取
"""

import logging
import asyncio
import json
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass

import requests
from app.services.robust_json_parser import parse_llm_json_response
from app.services.text_structure_analyzer import Character, get_text_structure_analyzer
from app.services.character_storage_service import character_storage_service

logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """模型配置"""
    name: str
    endpoint: str
    temperature: float = 0.7
    max_tokens: int = 2048
    timeout: int = 60


@dataclass
class VectorizationTask:
    """向量化任务"""
    project_id: str
    chapter_id: str
    content: str
    title: str = ""
    model_name: str = "qwen3:4b"
    extract_characters: bool = True
    extract_scenes: bool = True
    extract_themes: bool = True


class MultiModelVectorizer:
    """多模型向量化器"""
    
    def __init__(self):
        self.logger = logger
        self.ollama_base_url = "http://localhost:11434"
        
        # 预定义的模型配置
        self.models = {
            "qwen2.5:7b": ModelConfig(
                name="qwen2.5:7b",
                endpoint=f"{self.ollama_base_url}/api/generate",
                temperature=0.3,
                max_tokens=4096
            ),
            "llama3.1:8b": ModelConfig(
                name="llama3.1:8b", 
                endpoint=f"{self.ollama_base_url}/api/generate",
                temperature=0.5,
                max_tokens=3072
            ),
            "mistral:7b": ModelConfig(
                name="mistral:7b",
                endpoint=f"{self.ollama_base_url}/api/generate",
                temperature=0.4,
                max_tokens=2048
            ),
            "gemma2:9b": ModelConfig(
                name="gemma2:9b",
                endpoint=f"{self.ollama_base_url}/api/generate",
                temperature=0.6,
                max_tokens=3584
            )
        }
    
    async def vectorize_with_multiple_models(
        self, 
        task: VectorizationTask,
        model_names: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        使用多个模型进行向量化
        
        Args:
            task: 向量化任务
            model_names: 要使用的模型列表，如果为None则使用默认模型
            
        Returns:
            向量化结果字典
        """
        if model_names is None:
            model_names = [task.model_name]
        
        results = {}
        all_characters = []
        
        for model_name in model_names:
            try:
                self.logger.info(f"使用模型 {model_name} 进行向量化...")
                
                model_result = await self._vectorize_with_single_model(
                    task, model_name
                )
                
                results[model_name] = model_result
                
                # 收集所有提取的角色
                if model_result.get('characters'):
                    all_characters.extend(model_result['characters'])
                
                self.logger.info(f"模型 {model_name} 向量化完成")
                
            except Exception as e:
                self.logger.error(f"模型 {model_name} 向量化失败: {e}")
                results[model_name] = {
                    'success': False,
                    'error': str(e),
                    'characters': [],
                    'scenes': [],
                    'themes': []
                }
        
        # 合并和去重角色
        unique_characters = self._merge_and_deduplicate_characters(all_characters)
        
        # 保存角色到数据库
        if unique_characters and task.extract_characters:
            try:
                saved_characters = character_storage_service.save_characters(
                    project_id=task.project_id,
                    chapter_id=task.chapter_id,
                    characters=unique_characters
                )
                self.logger.info(f"保存了 {len(saved_characters)} 个角色到数据库")
            except Exception as e:
                self.logger.error(f"保存角色失败: {e}")
        
        return {
            'task_id': f"{task.project_id}_{task.chapter_id}",
            'models_used': model_names,
            'results': results,
            'merged_characters': [char.__dict__ for char in unique_characters],
            'total_characters': len(unique_characters),
            'success_count': sum(1 for r in results.values() if r.get('success', False)),
            'timestamp': datetime.now().isoformat()
        }
    
    async def _vectorize_with_single_model(
        self, 
        task: VectorizationTask, 
        model_name: str
    ) -> Dict[str, Any]:
        """使用单个模型进行向量化"""
        
        if model_name not in self.models:
            raise ValueError(f"不支持的模型: {model_name}")
        
        model_config = self.models[model_name]
        result = {
            'model': model_name,
            'success': False,
            'characters': [],
            'scenes': [],
            'themes': [],
            'processing_time': 0
        }
        
        start_time = datetime.now()
        
        try:
            # 提取角色
            if task.extract_characters:
                characters = await self._extract_characters_with_model(
                    task.content, model_config
                )
                result['characters'] = characters
            
            # 提取场景
            if task.extract_scenes:
                scenes = await self._extract_scenes_with_model(
                    task.content, model_config
                )
                result['scenes'] = scenes
            
            # 提取主题
            if task.extract_themes:
                themes = await self._extract_themes_with_model(
                    task.content, model_config
                )
                result['themes'] = themes
            
            result['success'] = True
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"模型 {model_name} 处理失败: {e}")
        
        finally:
            result['processing_time'] = (datetime.now() - start_time).total_seconds()
        
        return result
    
    async def _extract_characters_with_model(
        self, 
        content: str, 
        model_config: ModelConfig
    ) -> List[Character]:
        """使用指定模型提取角色"""
        
        prompt = f"""请分析以下小说内容，提取其中的角色信息。

内容：
{content}

请以JSON格式输出角色信息，格式如下：
{{
  "characters": [
    {{
      "name": "角色姓名",
      "description": "角色描述",
      "importance": "main/secondary/minor",
      "personality_traits": ["性格特征1", "性格特征2"],
      "appearance": "外貌描述",
      "background": "背景信息",
      "abilities": ["能力1", "能力2"],
      "occupation": "职业或身份"
    }}
  ]
}}

只输出JSON，不要其他内容："""
        
        response = await self._call_ollama_async(prompt, model_config)
        if not response:
            return []
        
        try:
            data = parse_llm_json_response(response)
            if not data:
                return []
            
            characters = []
            for char_data in data.get('characters', []):
                character = Character(
                    name=char_data.get('name', ''),
                    description=char_data.get('description', ''),
                    importance=char_data.get('importance', 'minor'),
                    personality_traits=char_data.get('personality_traits', []),
                    appearance=char_data.get('appearance', ''),
                    background=char_data.get('background', ''),
                    abilities=char_data.get('abilities', []),
                    occupation=char_data.get('occupation')
                )
                characters.append(character)
            
            return characters
            
        except Exception as e:
            self.logger.error(f"解析角色信息失败: {e}")
            return []
    
    async def _extract_scenes_with_model(
        self, 
        content: str, 
        model_config: ModelConfig
    ) -> List[Dict[str, Any]]:
        """使用指定模型提取场景"""
        # 简化实现，返回空列表
        return []
    
    async def _extract_themes_with_model(
        self, 
        content: str, 
        model_config: ModelConfig
    ) -> List[str]:
        """使用指定模型提取主题"""
        # 简化实现，返回空列表
        return []
    
    async def _call_ollama_async(
        self, 
        prompt: str, 
        model_config: ModelConfig
    ) -> Optional[str]:
        """异步调用Ollama API"""
        
        payload = {
            "model": model_config.name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": model_config.temperature,
                "num_predict": model_config.max_tokens
            }
        }
        
        try:
            # 使用asyncio在线程池中执行同步请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    model_config.endpoint,
                    json=payload,
                    timeout=model_config.timeout
                )
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                self.logger.error(f"Ollama API错误: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"调用Ollama API失败: {e}")
            return None
    
    def _merge_and_deduplicate_characters(
        self, 
        characters: List[Character]
    ) -> List[Character]:
        """合并和去重角色"""
        if not characters:
            return []
        
        # 按名称去重，保留信息最完整的版本
        character_dict = {}
        
        for char in characters:
            name = char.name.strip()
            if not name:
                continue
            
            if name not in character_dict:
                character_dict[name] = char
            else:
                # 合并信息，保留更完整的版本
                existing = character_dict[name]
                merged = self._merge_character_info(existing, char)
                character_dict[name] = merged
        
        return list(character_dict.values())
    
    def _merge_character_info(self, char1: Character, char2: Character) -> Character:
        """合并两个角色的信息"""
        return Character(
            name=char1.name,
            description=char1.description if len(char1.description) > len(char2.description) else char2.description,
            importance=char1.importance if char1.importance != 'minor' else char2.importance,
            personality_traits=list(set((char1.personality_traits or []) + (char2.personality_traits or []))),
            appearance=char1.appearance if char1.appearance else char2.appearance,
            background=char1.background if char1.background else char2.background,
            abilities=list(set((char1.abilities or []) + (char2.abilities or []))),
            occupation=char1.occupation if char1.occupation else char2.occupation,
            aliases=list(set((char1.aliases or []) + (char2.aliases or []))),
            gender=char1.gender if char1.gender else char2.gender,
            age=char1.age if char1.age else char2.age,
            specialties=list(set((char1.specialties or []) + (char2.specialties or []))),
            skills=list(set((char1.skills or []) + (char2.skills or []))),
            relationships=(char1.relationships or []) + (char2.relationships or []),
            first_appearance=char1.first_appearance if char1.first_appearance else char2.first_appearance
        )


# 全局实例
multi_model_vectorizer = MultiModelVectorizer()


def get_multi_model_vectorizer() -> MultiModelVectorizer:
    """获取多模型向量化器实例"""
    return multi_model_vectorizer
