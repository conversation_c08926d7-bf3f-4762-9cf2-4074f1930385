"""
增强向量化API路由
提供结构化解析、知识图谱构建、多层次向量化和智能查询的API接口
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from pydantic import BaseModel

from app.services.enhanced_chapter_vectorizer import get_enhanced_chapter_vectorizer

router = APIRouter(prefix="/projects/{project_id}/enhanced-vectorization", tags=["enhanced-vectorization"])

# 请求模型
class VectorizeChapterRequest(BaseModel):
    """章节向量化请求"""
    chapter_id: str
    content: str
    title: str = ""
    force_refresh: bool = False
    use_llm: bool = True  # 默认使用大模型进行分析

class VectorizeProjectRequest(BaseModel):
    """项目向量化请求"""
    force_refresh: bool = False

class EnhancedSearchRequest(BaseModel):
    """增强搜索请求"""
    query: str
    search_options: Dict[str, Any] = {}

class AnalyzeStructureRequest(BaseModel):
    """结构分析请求"""
    content: str
    use_llm: bool = True  # 默认使用大模型进行分析

# 响应模型
class VectorizationResultResponse(BaseModel):
    """向量化结果响应"""
    success: bool
    chapter_id: str
    project_id: str
    processing_time: float
    structured_data: Optional[Dict[str, Any]] = None
    knowledge_graph_stats: Optional[Dict[str, Any]] = None
    vector_stats: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

class ProjectVectorizationStatsResponse(BaseModel):
    """项目向量化统计响应"""
    project_id: str
    total_chapters: int
    vectorized_chapters: int
    total_entities: int
    total_relationships: int
    total_vectors: int
    last_updated: str
    processing_time: float

class VectorizationStatusResponse(BaseModel):
    """向量化状态响应"""
    project_id: str
    total_chapters: int
    vectorized_chapters: int
    vectorization_rate: float
    total_memories: int
    knowledge_graph: Dict[str, Any]
    memory_stats: Dict[str, Any]
    last_updated: Optional[str] = None

class EnhancedSearchResponse(BaseModel):
    """增强搜索响应"""
    query: str
    intent: Dict[str, Any]
    vector_results: List[Dict[str, Any]]
    graph_results: List[Dict[str, Any]]
    synthesized_answer: str
    confidence: float
    sources: List[str]
    reasoning_steps: List[str]

class ProjectInsightsResponse(BaseModel):
    """项目洞察响应"""
    vectorization_status: Dict[str, Any]
    knowledge_graph_analysis: Dict[str, Any]
    content_analysis: Dict[str, Any]
    recommendations: List[str]

# API端点
@router.post("/chapters/vectorize", response_model=VectorizationResultResponse)
async def vectorize_chapter(project_id: str, request: VectorizeChapterRequest):
    """向量化单个章节"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        
        result = await vectorizer.vectorize_chapter(
            project_id=project_id,
            chapter_id=request.chapter_id,
            content=request.content,
            title=request.title,
            use_llm=request.use_llm
        )
        
        # 手动构建响应对象
        return VectorizationResultResponse(
            success=result.success,
            chapter_id=result.chapter_id,
            project_id=result.project_id,
            processing_time=result.processing_time,
            structured_data=result.structured_data,
            knowledge_graph_stats=result.knowledge_graph_stats,
            vector_stats=result.vector_stats,
            error_message=result.error_message
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"章节向量化失败: {str(e)}"
        )

@router.post("/project/vectorize", response_model=ProjectVectorizationStatsResponse)
async def vectorize_project(project_id: str, request: VectorizeProjectRequest, 
                           background_tasks: BackgroundTasks):
    """向量化整个项目"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        
        # 对于大项目，可以在后台执行
        stats = await vectorizer.vectorize_project(
            project_id=project_id,
            force_refresh=request.force_refresh
        )
        
        return ProjectVectorizationStatsResponse(
            project_id=stats.project_id,
            total_chapters=stats.total_chapters,
            vectorized_chapters=stats.vectorized_chapters,
            total_entities=stats.total_entities,
            total_relationships=stats.total_relationships,
            total_vectors=stats.total_vectors,
            last_updated=stats.last_updated,
            processing_time=stats.processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"项目向量化失败: {str(e)}"
        )

@router.get("/status", response_model=VectorizationStatusResponse)
async def get_vectorization_status(project_id: str):
    """获取向量化状态"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        status_data = vectorizer.get_vectorization_status(project_id)
        
        if not status_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目向量化状态未找到"
            )
        
        return VectorizationStatusResponse(**status_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取向量化状态失败: {str(e)}"
        )

@router.post("/search", response_model=EnhancedSearchResponse)
async def enhanced_search(project_id: str, request: EnhancedSearchRequest):
    """增强搜索"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        
        result = vectorizer.search_enhanced(
            project_id=project_id,
            query=request.query,
            search_options=request.search_options
        )
        
        if 'error' in result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result['error']
            )
        
        return EnhancedSearchResponse(
            query=result['query'],
            intent=result.get('intent', {}),
            vector_results=result.get('vector_results', []),
            graph_results=result.get('graph_results', []),
            synthesized_answer=result.get('synthesized_answer', ''),
            confidence=result.get('confidence', 0.0),
            sources=result.get('sources', []),
            reasoning_steps=result.get('reasoning_steps', [])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"增强搜索失败: {str(e)}"
        )

@router.get("/search/suggestions")
async def get_search_suggestions(project_id: str, partial_query: str = ""):
    """获取搜索建议"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        suggestions = vectorizer.get_query_suggestions(project_id, partial_query)
        
        return {
            "suggestions": suggestions,
            "count": len(suggestions)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取搜索建议失败: {str(e)}"
        )

@router.post("/analyze/structure")
async def analyze_chapter_structure(project_id: str, request: AnalyzeStructureRequest):
    """分析章节结构"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        
        structure_data = vectorizer.analyze_chapter_structure(request.content, request.use_llm)
        
        if not structure_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="章节结构分析失败"
            )
        
        return {
            "success": True,
            "structure": structure_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"章节结构分析失败: {str(e)}"
        )

@router.get("/insights", response_model=ProjectInsightsResponse)
async def get_project_insights(project_id: str):
    """获取项目洞察"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        insights = vectorizer.get_project_insights(project_id)
        
        if not insights:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目洞察数据未找到"
            )
        
        return ProjectInsightsResponse(**insights)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目洞察失败: {str(e)}"
        )

@router.get("/knowledge-graph/stats")
async def get_knowledge_graph_stats(project_id: str):
    """获取知识图谱统计"""
    try:
        from app.services.knowledge_graph_manager import get_knowledge_graph_manager
        
        kg_manager = get_knowledge_graph_manager()
        stats = kg_manager.get_knowledge_graph_stats(project_id)
        
        return {
            "project_id": project_id,
            "stats": stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取知识图谱统计失败: {str(e)}"
        )

@router.get("/knowledge-graph/entity/{entity_name}/relationships")
async def get_entity_relationships(project_id: str, entity_name: str):
    """获取实体关系"""
    try:
        from app.services.knowledge_graph_manager import get_knowledge_graph_manager
        
        kg_manager = get_knowledge_graph_manager()
        relationships = kg_manager.query_entity_relationships(project_id, entity_name)
        
        return {
            "entity_name": entity_name,
            "relationships": relationships,
            "count": len(relationships)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取实体关系失败: {str(e)}"
        )

@router.get("/vectors/multi-level/search")
async def multi_level_vector_search(project_id: str, query: str, 
                                   levels: str = "0,1,2,3", limit: int = 10):
    """多层次向量搜索"""
    try:
        from app.services.multi_level_vectorizer import get_multi_level_vectorizer
        
        vectorizer = get_multi_level_vectorizer()
        
        # 解析层级参数
        level_list = [int(l.strip()) for l in levels.split(',') if l.strip().isdigit()]
        
        results = vectorizer.search_multi_level(
            project_id=project_id,
            query=query,
            levels=level_list,
            limit=limit
        )
        
        return {
            "query": query,
            "levels": level_list,
            "results": results,
            "count": len(results)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"多层次向量搜索失败: {str(e)}"
        )
