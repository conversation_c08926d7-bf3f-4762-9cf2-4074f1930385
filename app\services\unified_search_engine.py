"""
统一的多维度搜索引擎
整合所有维度的信息，实现智能搜索和问答系统
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

from app.services.intelligent_agent_service import get_intelligent_agent_service
from app.services.enhanced_chapter_vectorizer import get_enhanced_chapter_vectorizer
from app.services.chapter_index_service import get_chapter_index_service
from app.services.enhanced_project_service import get_enhanced_project_service
from app.services.ai_memory_service import get_ai_memory_service

logger = logging.getLogger(__name__)


class SearchScope(str, Enum):
    """搜索范围"""
    CHAPTERS = "chapters"           # 章节内容
    CHARACTERS = "characters"       # 角色信息
    WORLD = "world"                # 世界观设定
    PLOT = "plot"                  # 情节发展
    RELATIONSHIPS = "relationships" # 角色关系
    THEMES = "themes"              # 主题思想
    ALL = "all"                    # 全部范围


@dataclass
class UnifiedSearchQuery:
    """统一搜索查询"""
    project_id: str
    query_text: str
    search_scope: List[SearchScope] = None
    
    # 过滤条件
    chapter_range: Optional[tuple] = None  # (start, end) 章节范围
    characters: List[str] = None
    content_types: List[str] = None
    
    # 搜索选项
    search_mode: str = "intelligent"  # intelligent, semantic, keyword, hybrid
    max_results: int = 20
    include_content: bool = True
    similarity_threshold: float = 0.7
    
    def __post_init__(self):
        if self.search_scope is None:
            self.search_scope = [SearchScope.ALL]
        if self.characters is None:
            self.characters = []
        if self.content_types is None:
            self.content_types = []


@dataclass
class SearchResult:
    """搜索结果项"""
    result_id: str
    result_type: str  # chapter, character, world, plot, relationship, theme
    title: str
    content: str
    summary: str
    relevance_score: float
    
    # 元数据
    source: str = ""
    chapter_id: Optional[str] = None
    chapter_number: Optional[int] = None
    characters_involved: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.characters_involved is None:
            self.characters_involved = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class UnifiedSearchResponse:
    """统一搜索响应"""
    query: str
    total_results: int
    results: List[SearchResult]
    
    # 分类结果统计
    results_by_type: Dict[str, int] = None
    
    # AI生成的回答
    ai_answer: str = ""
    confidence: float = 0.0
    
    # 建议和相关查询
    suggestions: List[str] = None
    related_queries: List[str] = None
    
    # 执行信息
    search_time: float = 0.0
    search_mode: str = ""
    
    def __post_init__(self):
        if self.results_by_type is None:
            self.results_by_type = {}
        if self.suggestions is None:
            self.suggestions = []
        if self.related_queries is None:
            self.related_queries = []


class UnifiedSearchEngine:
    """统一搜索引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.agent_service = get_intelligent_agent_service()
        self.chapter_vectorizer = get_enhanced_chapter_vectorizer()
        self.chapter_index = get_chapter_index_service()
        self.project_service = get_enhanced_project_service()
        self.ai_memory = get_ai_memory_service()
    
    async def search(self, query: UnifiedSearchQuery) -> UnifiedSearchResponse:
        """统一搜索入口"""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始统一搜索: {query.query_text}")
            
            if query.search_mode == "intelligent":
                response = await self._intelligent_search(query)
            elif query.search_mode == "semantic":
                response = await self._semantic_search(query)
            elif query.search_mode == "keyword":
                response = await self._keyword_search(query)
            elif query.search_mode == "hybrid":
                response = await self._hybrid_search(query)
            else:
                response = await self._intelligent_search(query)  # 默认使用智能搜索
            
            # 计算搜索时间
            search_time = (datetime.now() - start_time).total_seconds()
            response.search_time = search_time
            response.search_mode = query.search_mode
            
            self.logger.info(f"搜索完成，返回 {response.total_results} 个结果，耗时 {search_time:.2f}秒")
            return response
            
        except Exception as e:
            search_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"统一搜索失败: {e}")
            
            return UnifiedSearchResponse(
                query=query.query_text,
                total_results=0,
                results=[],
                ai_answer=f"搜索时出现错误：{str(e)}",
                confidence=0.0,
                search_time=search_time,
                search_mode=query.search_mode
            )
    
    async def _intelligent_search(self, query: UnifiedSearchQuery) -> UnifiedSearchResponse:
        """智能搜索（使用Agent）"""
        try:
            # 使用智能Agent处理查询
            agent_response = await self.agent_service.process_user_query(
                project_id=query.project_id,
                user_query=query.query_text
            )
            
            # 转换Agent响应为统一搜索结果
            results = self._convert_agent_response_to_results(agent_response)
            
            # 应用过滤条件
            filtered_results = self._apply_filters(results, query)
            
            # 限制结果数量
            final_results = filtered_results[:query.max_results]
            
            # 统计结果类型
            results_by_type = self._count_results_by_type(final_results)
            
            # 生成相关查询建议
            related_queries = self._generate_related_queries(query, agent_response)
            
            return UnifiedSearchResponse(
                query=query.query_text,
                total_results=len(final_results),
                results=final_results,
                results_by_type=results_by_type,
                ai_answer=agent_response.response_text,
                confidence=agent_response.confidence,
                suggestions=agent_response.suggestions,
                related_queries=related_queries
            )
            
        except Exception as e:
            self.logger.error(f"智能搜索失败: {e}")
            raise
    
    async def _semantic_search(self, query: UnifiedSearchQuery) -> UnifiedSearchResponse:
        """语义搜索"""
        try:
            all_results = []
            
            # 根据搜索范围进行不同类型的搜索
            if SearchScope.ALL in query.search_scope or SearchScope.CHAPTERS in query.search_scope:
                chapter_results = await self._search_chapters(query)
                all_results.extend(chapter_results)
            
            if SearchScope.ALL in query.search_scope or SearchScope.CHARACTERS in query.search_scope:
                character_results = await self._search_characters(query)
                all_results.extend(character_results)
            
            if SearchScope.ALL in query.search_scope or SearchScope.WORLD in query.search_scope:
                world_results = await self._search_world_info(query)
                all_results.extend(world_results)
            
            if SearchScope.ALL in query.search_scope or SearchScope.PLOT in query.search_scope:
                plot_results = await self._search_plot_info(query)
                all_results.extend(plot_results)
            
            # 应用过滤条件
            filtered_results = self._apply_filters(all_results, query)
            
            # 按相关性排序
            filtered_results.sort(key=lambda x: x.relevance_score, reverse=True)
            
            # 限制结果数量
            final_results = filtered_results[:query.max_results]
            
            # 统计结果类型
            results_by_type = self._count_results_by_type(final_results)
            
            # 生成简单的AI回答
            ai_answer = self._generate_simple_answer(query, final_results)
            
            return UnifiedSearchResponse(
                query=query.query_text,
                total_results=len(final_results),
                results=final_results,
                results_by_type=results_by_type,
                ai_answer=ai_answer,
                confidence=0.7
            )
            
        except Exception as e:
            self.logger.error(f"语义搜索失败: {e}")
            raise
    
    async def _keyword_search(self, query: UnifiedSearchQuery) -> UnifiedSearchResponse:
        """关键词搜索"""
        try:
            # 实现关键词搜索逻辑
            # 这里可以使用更简单的字符串匹配方法
            results = await self._simple_keyword_search(query)
            
            # 应用过滤条件
            filtered_results = self._apply_filters(results, query)
            
            # 限制结果数量
            final_results = filtered_results[:query.max_results]
            
            # 统计结果类型
            results_by_type = self._count_results_by_type(final_results)
            
            return UnifiedSearchResponse(
                query=query.query_text,
                total_results=len(final_results),
                results=final_results,
                results_by_type=results_by_type,
                ai_answer=f"找到 {len(final_results)} 个包含关键词的结果",
                confidence=0.6
            )
            
        except Exception as e:
            self.logger.error(f"关键词搜索失败: {e}")
            raise
    
    async def _hybrid_search(self, query: UnifiedSearchQuery) -> UnifiedSearchResponse:
        """混合搜索"""
        try:
            # 同时执行语义搜索和关键词搜索
            semantic_query = UnifiedSearchQuery(**asdict(query))
            semantic_query.search_mode = "semantic"
            semantic_response = await self._semantic_search(semantic_query)
            
            keyword_query = UnifiedSearchQuery(**asdict(query))
            keyword_query.search_mode = "keyword"
            keyword_response = await self._keyword_search(keyword_query)
            
            # 合并结果
            merged_results = self._merge_search_results(
                semantic_response.results,
                keyword_response.results
            )
            
            # 限制结果数量
            final_results = merged_results[:query.max_results]
            
            # 统计结果类型
            results_by_type = self._count_results_by_type(final_results)
            
            # 选择更好的AI回答
            ai_answer = semantic_response.ai_answer if semantic_response.confidence > keyword_response.confidence else keyword_response.ai_answer
            confidence = max(semantic_response.confidence, keyword_response.confidence) * 0.9  # 混合搜索置信度稍低
            
            return UnifiedSearchResponse(
                query=query.query_text,
                total_results=len(final_results),
                results=final_results,
                results_by_type=results_by_type,
                ai_answer=ai_answer,
                confidence=confidence
            )
            
        except Exception as e:
            self.logger.error(f"混合搜索失败: {e}")
            raise

    def _convert_agent_response_to_results(self, agent_response) -> List[SearchResult]:
        """将Agent响应转换为搜索结果"""
        try:
            results = []
            retrieved_info = agent_response.retrieved_info

            # 处理章节信息
            if 'chapter_info' in retrieved_info:
                chapter_info = retrieved_info['chapter_info']
                if 'chapter_index' in chapter_info:
                    index = chapter_info['chapter_index']
                    content = chapter_info.get('chapter_content', {})

                    result = SearchResult(
                        result_id=f"chapter_{index.get('chapter_id', '')}",
                        result_type="chapter",
                        title=f"第{index.get('chapter_number', '?')}章 - {index.get('title', '')}",
                        content=content.get('content', ''),
                        summary=f"字数：{index.get('word_count', 0)}，角色：{', '.join(index.get('main_characters', []))}",
                        relevance_score=0.9,
                        source="chapter_index",
                        chapter_id=index.get('chapter_id'),
                        chapter_number=index.get('chapter_number'),
                        characters_involved=index.get('main_characters', []),
                        metadata=index
                    )
                    results.append(result)

            # 处理角色信息
            if 'character_info' in retrieved_info:
                char_info = retrieved_info['character_info']
                char_name = char_info.get('character_name', '')
                basic_info = char_info.get('basic_info', {})

                if basic_info:
                    result = SearchResult(
                        result_id=f"character_{char_name}",
                        result_type="character",
                        title=f"角色：{char_name}",
                        content=basic_info.get('description', ''),
                        summary=f"性格：{', '.join(basic_info.get('personality_tags', []))}",
                        relevance_score=0.85,
                        source="character_service",
                        characters_involved=[char_name],
                        metadata=basic_info
                    )
                    results.append(result)

            # 处理世界观信息
            if 'world_info' in retrieved_info:
                world_info = retrieved_info['world_info']
                for result_data in world_info.get('results', []):
                    result = SearchResult(
                        result_id=f"world_{result_data.get('title', '').replace(' ', '_')}",
                        result_type="world",
                        title=result_data.get('title', ''),
                        content=result_data.get('content', ''),
                        summary=result_data.get('content', '')[:100] + '...',
                        relevance_score=0.8,
                        source="project_info",
                        metadata=result_data.get('metadata', {})
                    )
                    results.append(result)

            # 处理记忆信息
            if 'memories' in retrieved_info:
                memories = retrieved_info['memories']
                for memory_data in memories.get('memories', []):
                    result = SearchResult(
                        result_id=f"memory_{memory_data.get('id', '')}",
                        result_type=memory_data.get('type', 'memory'),
                        title=memory_data.get('title', ''),
                        content=memory_data.get('content', ''),
                        summary=memory_data.get('summary', ''),
                        relevance_score=0.75,
                        source="ai_memory",
                        chapter_id=memory_data.get('chapter_id'),
                        metadata=memory_data.get('metadata', {})
                    )
                    results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"转换Agent响应失败: {e}")
            return []

    async def _search_chapters(self, query: UnifiedSearchQuery) -> List[SearchResult]:
        """搜索章节"""
        try:
            from app.services.chapter_index_service import ChapterSearchQuery

            search_query = ChapterSearchQuery(
                project_id=query.project_id,
                query_text=query.query_text,
                search_type="semantic",
                limit=query.max_results // 2
            )

            chapter_results = await self.chapter_index.search_chapters(search_query)

            results = []
            for chapter_result in chapter_results:
                index = chapter_result.chapter_index
                result = SearchResult(
                    result_id=f"chapter_{index.chapter_id}",
                    result_type="chapter",
                    title=f"第{index.chapter_number}章 - {index.title}",
                    content=chapter_result.content_snippet,
                    summary=f"字数：{index.word_count}，角色：{', '.join(index.main_characters)}",
                    relevance_score=chapter_result.relevance_score,
                    source="chapter_search",
                    chapter_id=index.chapter_id,
                    chapter_number=index.chapter_number,
                    characters_involved=index.main_characters,
                    metadata=asdict(index)
                )
                results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"搜索章节失败: {e}")
            return []

    async def _search_characters(self, query: UnifiedSearchQuery) -> List[SearchResult]:
        """搜索角色"""
        try:
            memories = await self.ai_memory.search_memories(
                project_id=query.project_id,
                query=query.query_text,
                memory_types=['character'],
                limit=query.max_results // 4
            )

            results = []
            for memory in memories:
                result = SearchResult(
                    result_id=f"character_memory_{memory.id}",
                    result_type="character",
                    title=memory.title,
                    content=memory.content,
                    summary=memory.summary,
                    relevance_score=0.8,
                    source="character_memory",
                    chapter_id=memory.chapter_id,
                    metadata=memory.metadata
                )
                results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"搜索角色失败: {e}")
            return []

    async def _search_world_info(self, query: UnifiedSearchQuery) -> List[SearchResult]:
        """搜索世界观信息"""
        try:
            from app.services.enhanced_project_service import ProjectInfoSearchQuery

            search_query = ProjectInfoSearchQuery(
                project_id=query.project_id,
                query_text=query.query_text,
                info_types=['world'],
                limit=query.max_results // 4
            )

            world_results = await self.project_service.search_project_info(search_query)

            results = []
            for world_result in world_results:
                result = SearchResult(
                    result_id=f"world_{world_result.title.replace(' ', '_')}",
                    result_type="world",
                    title=world_result.title,
                    content=world_result.content,
                    summary=world_result.content[:100] + '...',
                    relevance_score=world_result.relevance_score,
                    source="world_info",
                    metadata=world_result.metadata
                )
                results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"搜索世界观信息失败: {e}")
            return []

    async def _search_plot_info(self, query: UnifiedSearchQuery) -> List[SearchResult]:
        """搜索情节信息"""
        try:
            memories = await self.ai_memory.search_memories(
                project_id=query.project_id,
                query=query.query_text,
                memory_types=['plot'],
                limit=query.max_results // 4
            )

            results = []
            for memory in memories:
                result = SearchResult(
                    result_id=f"plot_memory_{memory.id}",
                    result_type="plot",
                    title=memory.title,
                    content=memory.content,
                    summary=memory.summary,
                    relevance_score=0.8,
                    source="plot_memory",
                    chapter_id=memory.chapter_id,
                    metadata=memory.metadata
                )
                results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"搜索情节信息失败: {e}")
            return []

    async def _simple_keyword_search(self, query: UnifiedSearchQuery) -> List[SearchResult]:
        """简单关键词搜索"""
        try:
            keywords = query.query_text.lower().split()
            results = []

            # 搜索所有记忆
            all_memories = await self.ai_memory.search_memories(
                project_id=query.project_id,
                query="",  # 空查询获取所有记忆
                limit=100
            )

            for memory in all_memories:
                content_lower = memory.content.lower()
                title_lower = memory.title.lower()

                # 计算关键词匹配分数
                score = 0.0
                for keyword in keywords:
                    if keyword in title_lower:
                        score += 0.3
                    if keyword in content_lower:
                        score += 0.2

                if score > 0:
                    result = SearchResult(
                        result_id=f"keyword_{memory.id}",
                        result_type=memory.type,
                        title=memory.title,
                        content=memory.content,
                        summary=memory.summary,
                        relevance_score=score,
                        source="keyword_search",
                        chapter_id=memory.chapter_id,
                        metadata=memory.metadata
                    )
                    results.append(result)

            # 按分数排序
            results.sort(key=lambda x: x.relevance_score, reverse=True)

            return results

        except Exception as e:
            self.logger.error(f"关键词搜索失败: {e}")
            return []

    def _apply_filters(self, results: List[SearchResult], query: UnifiedSearchQuery) -> List[SearchResult]:
        """应用过滤条件"""
        try:
            filtered = []

            for result in results:
                # 章节范围过滤
                if query.chapter_range and result.chapter_number:
                    start, end = query.chapter_range
                    if not (start <= result.chapter_number <= end):
                        continue

                # 角色过滤
                if query.characters:
                    if not any(char in result.characters_involved for char in query.characters):
                        continue

                # 内容类型过滤
                if query.content_types:
                    if result.result_type not in query.content_types:
                        continue

                filtered.append(result)

            return filtered

        except Exception as e:
            self.logger.error(f"应用过滤条件失败: {e}")
            return results

    def _count_results_by_type(self, results: List[SearchResult]) -> Dict[str, int]:
        """统计结果类型"""
        try:
            counts = {}
            for result in results:
                result_type = result.result_type
                counts[result_type] = counts.get(result_type, 0) + 1
            return counts

        except Exception as e:
            self.logger.error(f"统计结果类型失败: {e}")
            return {}

    def _merge_search_results(self, results1: List[SearchResult], results2: List[SearchResult]) -> List[SearchResult]:
        """合并搜索结果"""
        try:
            merged = {}

            # 添加第一组结果
            for result in results1:
                merged[result.result_id] = result
                merged[result.result_id].relevance_score *= 0.6  # 语义搜索权重

            # 合并第二组结果
            for result in results2:
                if result.result_id in merged:
                    # 合并分数
                    merged[result.result_id].relevance_score += result.relevance_score * 0.4
                else:
                    result.relevance_score *= 0.4  # 关键词搜索权重
                    merged[result.result_id] = result

            # 按分数排序
            merged_list = list(merged.values())
            merged_list.sort(key=lambda x: x.relevance_score, reverse=True)

            return merged_list

        except Exception as e:
            self.logger.error(f"合并搜索结果失败: {e}")
            return results1 + results2

    def _generate_simple_answer(self, query: UnifiedSearchQuery, results: List[SearchResult]) -> str:
        """生成简单的AI回答"""
        try:
            if not results:
                return "抱歉，没有找到相关信息。"

            result_types = self._count_results_by_type(results)
            type_descriptions = {
                'chapter': '章节',
                'character': '角色',
                'world': '世界观',
                'plot': '情节',
                'relationship': '关系',
                'theme': '主题'
            }

            answer_parts = [f"找到了 {len(results)} 个相关结果："]

            for result_type, count in result_types.items():
                type_name = type_descriptions.get(result_type, result_type)
                answer_parts.append(f"- {type_name}信息：{count}条")

            # 添加最相关的结果摘要
            if results:
                top_result = results[0]
                answer_parts.append(f"\n最相关的结果：{top_result.title}")
                if top_result.summary:
                    answer_parts.append(top_result.summary[:100] + '...')

            return '\n'.join(answer_parts)

        except Exception as e:
            self.logger.error(f"生成简单回答失败: {e}")
            return f"找到了 {len(results)} 个相关结果。"

    def _generate_related_queries(self, query: UnifiedSearchQuery, agent_response) -> List[str]:
        """生成相关查询建议"""
        try:
            related = []

            # 基于意图生成相关查询
            intent = agent_response.intent

            if 'chapter_number' in intent.entities:
                chapter_nums = intent.entities['chapter_number']
                for num in chapter_nums:
                    if num > 1:
                        related.append(f"第{num-1}章说了什么")
                    related.append(f"第{num+1}章说了什么")

            if 'character_name' in intent.entities:
                char_names = intent.entities['character_name']
                for char in char_names:
                    related.append(f"{char}在其他章节的表现")
                    related.append(f"{char}的角色关系")

            # 添加通用相关查询
            related.extend([
                "项目的世界观设定",
                "主要角色关系网络",
                "故事的主要情节线"
            ])

            return related[:5]  # 最多返回5个

        except Exception as e:
            self.logger.error(f"生成相关查询失败: {e}")
            return []

    async def get_project_overview(self, project_id: str) -> Dict[str, Any]:
        """获取项目概览"""
        try:
            # 获取项目基本统计
            overview_query = UnifiedSearchQuery(
                project_id=project_id,
                query_text="项目概览",
                search_scope=[SearchScope.ALL],
                max_results=50
            )

            response = await self.search(overview_query)

            # 统计信息
            stats = {
                'total_results': response.total_results,
                'results_by_type': response.results_by_type,
                'chapters': len([r for r in response.results if r.result_type == 'chapter']),
                'characters': len([r for r in response.results if r.result_type == 'character']),
                'world_elements': len([r for r in response.results if r.result_type == 'world']),
                'plot_elements': len([r for r in response.results if r.result_type == 'plot'])
            }

            return {
                'project_id': project_id,
                'statistics': stats,
                'recent_results': response.results[:10],
                'suggestions': [
                    "查看所有章节列表",
                    "了解主要角色信息",
                    "查看世界观设定",
                    "分析故事情节发展"
                ]
            }

        except Exception as e:
            self.logger.error(f"获取项目概览失败: {e}")
            return {'error': str(e)}


# 全局实例
unified_search_engine = UnifiedSearchEngine()


def get_unified_search_engine() -> UnifiedSearchEngine:
    """获取统一搜索引擎实例"""
    return unified_search_engine
