#!/usr/bin/env python3
"""
强制重新向量化章节并提取角色
"""

import requests
import json

def force_vectorize_chapter():
    """强制重新向量化章节"""
    url = 'http://localhost:8000/api/v1/projects/63ae07ad-2bc6-4354-92a9-db4be8160215/enhanced-vectorization/chapters/vectorize'
    
    payload = {
        'chapter_id': 'cdb529c3-3463-4aef-94b9-935acb23bbc9',
        'title': '重生之阴影',
        'content': '''墨深是一个记忆修复师，他拥有强大的技术能力。Zero是他的AI助手，总是在关键时刻提供帮助。在这个科幻世界中，墨深发现了一个神秘的女人，她戴着银色面具，眼神冷漠而神秘。这个女人似乎与某个重大的阴谋有关。墨深必须利用他的技能来解开这个谜团。''',
        'use_llm': True,
        'force_refresh': True
    }
    
    try:
        print("🚀 开始强制重新向量化章节...")
        response = requests.post(url, json=payload, timeout=60)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 向量化成功!")
            print(f"⏱️ 处理时间: {result.get('processing_time', 'N/A')} 秒")
            
            # 检查角色提取
            structured_data = result.get('structured_data', {})
            characters = structured_data.get('characters', [])
            print(f"👥 提取的角色数量: {len(characters)}")
            
            for i, char in enumerate(characters, 1):
                print(f"  {i}. {char.get('name', 'Unknown')}")
                print(f"     描述: {char.get('description', 'N/A')[:50]}...")
                print(f"     重要性: {char.get('importance', 'N/A')}")
                
            return len(characters) > 0
        else:
            print(f"❌ 向量化失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def check_characters_after_vectorization():
    """检查向量化后的角色数据"""
    print("\n🔍 检查向量化后的角色数据...")
    
    try:
        # 检查基础角色API
        url = 'http://localhost:8000/api/v1/projects/63ae07ad-2bc6-4354-92a9-db4be8160215/characters'
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            characters = response.json()
            print(f"📝 基础角色API返回 {len(characters)} 个角色")
            
            for char in characters:
                print(f"  - {char.get('name', 'Unknown')} ({char.get('role', 'N/A')})")
        else:
            print(f"❌ 基础角色API失败: {response.text}")
        
        # 检查高级角色API
        url = 'http://localhost:8000/api/v1/characters/63ae07ad-2bc6-4354-92a9-db4be8160215'
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            characters = response.json()
            print(f"📝 高级角色API返回 {len(characters)} 个角色")
            
            for char in characters:
                print(f"  - {char.get('name', 'Unknown')} (重要性: {char.get('importance_score', 'N/A')})")
        else:
            print(f"❌ 高级角色API失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 检查角色数据时出错: {e}")

if __name__ == "__main__":
    print("🎯 强制重新向量化章节并提取角色")
    print("="*50)
    
    success = force_vectorize_chapter()
    
    if success:
        print("\n✅ 向量化成功，检查角色数据...")
        check_characters_after_vectorization()
    else:
        print("\n❌ 向量化失败，请检查后端服务和LLM配置")
    
    print("\n" + "="*50)
    print("完成!")
