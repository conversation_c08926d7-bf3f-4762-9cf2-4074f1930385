"""
增强的AI功能路由
提供多维度向量化和智能搜索的API接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from app.services.enhanced_chapter_vectorizer import get_enhanced_chapter_vectorizer
from app.services.chapter_index_service import get_chapter_index_service
from app.services.enhanced_project_service import get_enhanced_project_service
from app.services.intelligent_agent_service import get_intelligent_agent_service
from app.services.unified_search_engine import get_unified_search_engine, UnifiedSearchQuery, SearchScope

router = APIRouter()

# 请求模型
class MultiDimensionVectorizeRequest(BaseModel):
    project_id: str
    chapter_id: str
    dimensions: Optional[List[str]] = None
    enable_multi_dimension: bool = True

class ChapterIndexRequest(BaseModel):
    project_id: str
    chapter_id: str

class ProjectVectorizeRequest(BaseModel):
    project_id: str

class AgentQueryRequest(BaseModel):
    project_id: str
    query: str

class UnifiedSearchRequest(BaseModel):
    project_id: str
    query_text: str
    search_scope: Optional[List[str]] = None
    search_mode: str = "intelligent"
    max_results: int = 20
    chapter_range: Optional[tuple] = None
    characters: Optional[List[str]] = None

# 响应模型
class VectorizeResponse(BaseModel):
    success: bool
    message: str
    processing_time: float
    stats: Optional[Dict[str, Any]] = None

class AgentResponse(BaseModel):
    success: bool
    response: str
    confidence: float
    suggestions: List[str]
    execution_time: float
    debug_info: Optional[Dict[str, Any]] = None

class SearchResponse(BaseModel):
    query: str
    total_results: int
    results: List[Dict[str, Any]]
    ai_answer: str
    confidence: float
    suggestions: List[str]
    search_time: float


@router.post("/vectorize/multi-dimension", response_model=VectorizeResponse)
async def vectorize_chapter_multi_dimension(request: MultiDimensionVectorizeRequest):
    """多维度向量化章节"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        
        # 获取章节内容
        from app.services.chapter_service import chapter_service
        chapter = chapter_service.get_chapter(request.project_id, request.chapter_id)
        if not chapter:
            raise HTTPException(status_code=404, detail="章节不存在")
        
        # 执行多维度向量化
        result = await vectorizer.vectorize_chapter(
            project_id=request.project_id,
            chapter_id=request.chapter_id,
            content=chapter.content,
            title=chapter.title,
            use_llm=True,
            enable_multi_dimension=request.enable_multi_dimension
        )
        
        if result.success:
            return VectorizeResponse(
                success=True,
                message=f"章节 {request.chapter_id} 多维度向量化完成",
                processing_time=result.processing_time,
                stats=result.vector_stats
            )
        else:
            return VectorizeResponse(
                success=False,
                message=f"向量化失败: {result.error_message}",
                processing_time=result.processing_time
            )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"向量化失败: {str(e)}")


@router.post("/index/chapter", response_model=VectorizeResponse)
async def build_chapter_index(request: ChapterIndexRequest):
    """构建章节索引"""
    try:
        index_service = get_chapter_index_service()
        
        # 构建章节索引
        index = await index_service.build_chapter_index(
            project_id=request.project_id,
            chapter_id=request.chapter_id
        )
        
        return VectorizeResponse(
            success=True,
            message=f"章节索引构建完成",
            processing_time=0.0,
            stats={
                'chapter_number': index.chapter_number,
                'word_count': index.word_count,
                'characters_count': len(index.main_characters),
                'scenes_count': len(index.scenes),
                'content_types': index.content_types
            }
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"构建索引失败: {str(e)}")


@router.post("/vectorize/project", response_model=VectorizeResponse)
async def vectorize_project_info(request: ProjectVectorizeRequest, background_tasks: BackgroundTasks):
    """向量化项目全局信息"""
    try:
        project_service = get_enhanced_project_service()
        
        # 在后台执行向量化
        background_tasks.add_task(
            project_service.vectorize_project_info,
            request.project_id
        )
        
        return VectorizeResponse(
            success=True,
            message="项目信息向量化已开始，将在后台执行",
            processing_time=0.0
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动项目向量化失败: {str(e)}")


@router.post("/agent/query", response_model=AgentResponse)
async def agent_query(request: AgentQueryRequest):
    """智能Agent查询"""
    try:
        agent_service = get_intelligent_agent_service()
        
        # 处理用户查询
        response = await agent_service.process_user_query(
            project_id=request.project_id,
            user_query=request.query
        )
        
        return AgentResponse(
            success=response.success,
            response=response.response_text,
            confidence=response.confidence,
            suggestions=response.suggestions,
            execution_time=response.execution_time,
            debug_info={
                'intent_type': response.intent.intent_type.value,
                'entities': response.intent.entities,
                'actions_taken': len(response.actions_taken)
            } if response.success else None
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Agent查询失败: {str(e)}")


@router.post("/search/unified", response_model=SearchResponse)
async def unified_search(request: UnifiedSearchRequest):
    """统一智能搜索"""
    try:
        search_engine = get_unified_search_engine()
        
        # 转换搜索范围
        search_scope = []
        if request.search_scope:
            for scope in request.search_scope:
                try:
                    search_scope.append(SearchScope(scope))
                except ValueError:
                    continue
        else:
            search_scope = [SearchScope.ALL]
        
        # 构建搜索查询
        query = UnifiedSearchQuery(
            project_id=request.project_id,
            query_text=request.query_text,
            search_scope=search_scope,
            search_mode=request.search_mode,
            max_results=request.max_results,
            chapter_range=request.chapter_range,
            characters=request.characters or []
        )
        
        # 执行搜索
        response = await search_engine.search(query)
        
        # 转换结果格式
        results = []
        for result in response.results:
            results.append({
                'id': result.result_id,
                'type': result.result_type,
                'title': result.title,
                'content': result.content[:500] + '...' if len(result.content) > 500 else result.content,
                'summary': result.summary,
                'relevance_score': result.relevance_score,
                'source': result.source,
                'chapter_number': result.chapter_number,
                'characters_involved': result.characters_involved,
                'metadata': result.metadata
            })
        
        return SearchResponse(
            query=response.query,
            total_results=response.total_results,
            results=results,
            ai_answer=response.ai_answer,
            confidence=response.confidence,
            suggestions=response.suggestions,
            search_time=response.search_time
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/project/{project_id}/overview")
async def get_project_overview(project_id: str):
    """获取项目概览"""
    try:
        search_engine = get_unified_search_engine()
        
        overview = await search_engine.get_project_overview(project_id)
        
        return overview
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目概览失败: {str(e)}")


@router.get("/project/{project_id}/vectorization-status")
async def get_vectorization_status(project_id: str):
    """获取向量化状态"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        
        status = vectorizer.get_vectorization_status(project_id)
        
        return status
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取向量化状态失败: {str(e)}")


@router.post("/project/{project_id}/vectorize-all")
async def vectorize_all_chapters(project_id: str, background_tasks: BackgroundTasks):
    """向量化项目所有章节"""
    try:
        vectorizer = get_enhanced_chapter_vectorizer()
        
        # 在后台执行完整的项目向量化
        background_tasks.add_task(
            vectorizer.vectorize_project,
            project_id,
            True  # force_refresh
        )
        
        return {
            "success": True,
            "message": "项目完整向量化已开始，将在后台执行",
            "project_id": project_id
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动项目向量化失败: {str(e)}")


@router.get("/search/suggestions/{project_id}")
async def get_search_suggestions(project_id: str, query: str = ""):
    """获取搜索建议"""
    try:
        # 基于项目内容生成搜索建议
        suggestions = [
            "第一章说了什么内容",
            "主角的性格特征如何",
            "世界观的魔法系统",
            "角色之间的关系网络",
            "故事的主要情节线",
            "各章节的情感基调",
            "重要场景的描述",
            "角色的成长轨迹"
        ]
        
        # 如果有查询文本，可以基于查询生成更相关的建议
        if query:
            # 这里可以添加基于查询的智能建议生成逻辑
            pass
        
        return {
            "suggestions": suggestions,
            "project_id": project_id
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取搜索建议失败: {str(e)}")


# 测试端点
@router.post("/test/agent")
async def test_agent_functionality(project_id: str):
    """测试Agent功能"""
    try:
        agent_service = get_intelligent_agent_service()
        
        test_queries = [
            "第一章说了什么内容",
            "主角是谁",
            "世界观设定如何",
            "角色关系网络"
        ]
        
        results = []
        for query in test_queries:
            response = await agent_service.process_user_query(project_id, query)
            results.append({
                'query': query,
                'success': response.success,
                'response': response.response_text[:200] + '...' if len(response.response_text) > 200 else response.response_text,
                'confidence': response.confidence,
                'execution_time': response.execution_time
            })
        
        return {
            "test_results": results,
            "project_id": project_id
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试Agent功能失败: {str(e)}")
