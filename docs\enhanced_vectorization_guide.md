# 增强的多维度向量化系统使用指南

## 概述

本系统实现了真正的AI Agent模式，支持多维度章节分析、智能索引构建和统一搜索引擎。用户可以通过自然语言与AI对话，获取项目的各种信息。

## 核心功能

### 1. 多维度章节分析

每个章节可以进行多次不同维度的分析：

- **角色分析**: 提取角色信息、性格特征、关系变化
- **情节分析**: 分析情节发展、冲突、转折点
- **场景分析**: 分析场景设定、环境描述、氛围营造
- **情感分析**: 分析情感基调、角色情感变化
- **主题分析**: 分析主题思想、象征意义
- **世界观分析**: 提取世界观设定、规则、背景信息

### 2. 智能章节索引

为每个章节构建完整的索引：

- 章节号和标题
- 主要角色列表
- 场景信息
- 情节要点
- 情感基调
- 主题标签
- 内容类型分类

### 3. 项目全局信息管理

向量化项目的全局信息：

- 项目名称和描述
- 世界观设定
- 角色关系网络
- 主要情节线
- 主题思想

### 4. 智能Agent系统

理解用户意图并自动获取相关信息：

- 意图识别（章节查询、角色查询、世界观查询等）
- 实体提取（章节号、角色名、关系等）
- 动作规划和执行
- 智能响应生成

### 5. 统一搜索引擎

整合所有维度的信息：

- 智能搜索（使用Agent）
- 语义搜索
- 关键词搜索
- 混合搜索

## API使用示例

### 1. 多维度向量化章节

```python
# 向量化单个章节
POST /api/vectorize/multi-dimension
{
    "project_id": "your-project-id",
    "chapter_id": "chapter-1",
    "enable_multi_dimension": true,
    "dimensions": ["character_analysis", "plot_analysis", "scene_analysis"]
}
```

### 2. 构建章节索引

```python
# 为章节构建索引
POST /api/index/chapter
{
    "project_id": "your-project-id",
    "chapter_id": "chapter-1"
}
```

### 3. 向量化项目信息

```python
# 向量化项目全局信息
POST /api/vectorize/project
{
    "project_id": "your-project-id"
}
```

### 4. 智能Agent查询

```python
# 使用自然语言查询
POST /api/agent/query
{
    "project_id": "your-project-id",
    "query": "第一章说了什么内容"
}

# 响应示例
{
    "success": true,
    "response": "第一章《开端》主要讲述了主角李明在现代都市中的平凡生活，以及他意外发现神秘力量的过程。章节包含3个主要角色，字数约2500字，情感基调为神秘和期待。",
    "confidence": 0.85,
    "suggestions": [
        "可以询问第二章的内容",
        "可以了解主角李明的性格特征",
        "可以询问神秘力量的具体设定"
    ],
    "execution_time": 1.2
}
```

### 5. 统一搜索

```python
# 智能搜索
POST /api/search/unified
{
    "project_id": "your-project-id",
    "query_text": "主角的性格特征",
    "search_mode": "intelligent",
    "search_scope": ["characters", "chapters"],
    "max_results": 10
}

# 响应示例
{
    "query": "主角的性格特征",
    "total_results": 5,
    "results": [
        {
            "id": "character_李明",
            "type": "character",
            "title": "角色：李明",
            "content": "李明是一个内向但坚韧的年轻人...",
            "summary": "性格：内向、坚韧、好奇",
            "relevance_score": 0.92,
            "characters_involved": ["李明"]
        }
    ],
    "ai_answer": "主角李明的性格特征主要包括内向、坚韧和强烈的好奇心。他在面对困难时表现出不屈不挠的精神，同时对未知事物充满探索欲望。",
    "confidence": 0.88,
    "suggestions": [
        "可以了解李明在各章节的表现",
        "可以询问李明与其他角色的关系"
    ]
}
```

## 典型对话示例

### 章节内容查询

**用户**: "第一章说了什么内容"

**AI**: "第一章《开端》主要讲述了主角李明在现代都市中的平凡生活，以及他意外发现神秘力量的过程。章节包含3个主要角色（李明、张小雨、神秘老人），发生在都市公园和李明的公寓两个主要场景。情节围绕李明发现古老符文展开，情感基调为神秘和期待。字数约2500字。"

### 角色信息查询

**用户**: "李明这个角色怎么样"

**AI**: "李明是本故事的主角，性格内向但坚韧，具有强烈的好奇心和探索欲望。他在第1、3、5章中都有重要表现，与张小雨是青梅竹马的关系，与神秘老人是师徒关系。他的角色发展轨迹显示了从普通人到觉醒者的转变过程。"

### 世界观查询

**用户**: "这个世界的魔法系统是怎样的"

**AI**: "这个世界采用符文魔法系统，魔法力量来源于古老的符文文字。魔法师需要通过学习和理解符文的含义来释放法术。系统分为五个等级：学徒、法师、大法师、贤者、传说。每个符文都有特定的元素属性和能量强度，使用时需要消耗精神力。"

### 关系网络查询

**用户**: "角色之间的关系网络"

**AI**: "主要角色关系网络如下：
- 李明（主角）与张小雨：青梅竹马，互相关心
- 李明与神秘老人：师徒关系，老人传授符文知识
- 张小雨与林教授：师生关系，林教授是张小雨的导师
- 神秘老人与暗影组织：敌对关系，存在历史恩怨
整个关系网络围绕李明展开，形成了复杂的人际关系图谱。"

## 系统架构

### 核心组件

1. **EnhancedChapterVectorizer**: 多维度章节向量化器
2. **ChapterIndexService**: 章节索引服务
3. **EnhancedProjectService**: 增强项目信息管理
4. **IntelligentAgentService**: 智能Agent服务
5. **UnifiedSearchEngine**: 统一搜索引擎

### 数据流程

1. **输入**: 章节内容、项目信息
2. **多维度分析**: 使用LLM进行6个维度的分析
3. **向量化**: 将分析结果转换为向量并存储
4. **索引构建**: 为快速检索构建索引
5. **智能查询**: 用户查询 → 意图识别 → 信息检索 → 响应生成

### 技术特点

- **多模态分析**: 支持多种分析维度
- **智能意图识别**: 理解用户真实需求
- **动态信息整合**: 实时整合多源信息
- **自然语言交互**: 支持自然语言对话
- **可扩展架构**: 易于添加新的分析维度

## 最佳实践

### 1. 向量化策略

- 优先向量化重要章节
- 定期更新项目全局信息
- 根据需要选择分析维度

### 2. 查询优化

- 使用具体的问题而非模糊查询
- 善用章节号、角色名等实体
- 结合多种搜索模式

### 3. 系统维护

- 定期检查向量化状态
- 监控搜索性能
- 更新分析模型

## 故障排除

### 常见问题

1. **向量化失败**: 检查LLM服务状态和章节内容格式
2. **搜索结果不准确**: 调整相似度阈值和搜索参数
3. **Agent响应慢**: 优化查询复杂度和结果数量限制

### 性能优化

- 使用缓存减少重复计算
- 批量处理提高效率
- 异步执行长时间任务

## 未来扩展

- 支持更多分析维度
- 增强多语言支持
- 集成更多AI模型
- 优化搜索算法
- 添加可视化界面
