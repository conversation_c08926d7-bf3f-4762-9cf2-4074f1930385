"""
智能Agent服务
实现真正的Agent模式，能够理解用户意图并自动获取相关信息
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

from app.services.ai_model_service import ai_model_service
from app.services.chapter_index_service import get_chapter_index_service, ChapterSearchQuery
from app.services.enhanced_project_service import get_enhanced_project_service, ProjectInfoSearchQuery
from app.services.ai_memory_service import get_ai_memory_service
from app.services.chapter_service import chapter_service
from app.services.character_service import character_service
from app.models.memory import MemoryType

logger = logging.getLogger(__name__)


class IntentType(str, Enum):
    """意图类型"""
    CHAPTER_QUERY = "chapter_query"          # 章节查询
    CHARACTER_QUERY = "character_query"      # 角色查询
    PLOT_QUERY = "plot_query"               # 情节查询
    WORLD_QUERY = "world_query"             # 世界观查询
    RELATIONSHIP_QUERY = "relationship_query" # 关系查询
    SUMMARY_REQUEST = "summary_request"      # 总结请求
    ANALYSIS_REQUEST = "analysis_request"    # 分析请求
    WRITING_HELP = "writing_help"           # 写作帮助
    GENERAL_CHAT = "general_chat"           # 一般对话


@dataclass
class UserIntent:
    """用户意图"""
    intent_type: IntentType
    confidence: float
    entities: Dict[str, List[str]]  # 提取的实体
    parameters: Dict[str, Any]      # 意图参数
    original_query: str


@dataclass
class AgentAction:
    """Agent动作"""
    action_type: str
    description: str
    parameters: Dict[str, Any]
    priority: int = 1  # 1-5，5最高


@dataclass
class AgentResponse:
    """Agent响应"""
    success: bool
    response_text: str
    intent: UserIntent
    actions_taken: List[AgentAction]
    retrieved_info: Dict[str, Any]
    confidence: float
    suggestions: List[str] = None
    execution_time: float = 0.0
    
    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []


class IntelligentAgentService:
    """智能Agent服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.chapter_index = get_chapter_index_service()
        self.project_service = get_enhanced_project_service()
        self.ai_memory = get_ai_memory_service()
        
        # 意图识别模式
        self.intent_patterns = {
            IntentType.CHAPTER_QUERY: [
                r'第(\d+)章.*说了什么',
                r'第(\d+)章.*内容',
                r'第(\d+)章.*发生了什么',
                r'章节.*(\d+).*情况',
                r'(\d+)章.*讲的是什么',
            ],
            IntentType.CHARACTER_QUERY: [
                r'(.+?)这个角色.*怎么样',
                r'(.+?)的性格.*如何',
                r'(.+?)在.*章.*表现',
                r'角色(.+?).*信息',
                r'(.+?)这个人.*什么样',
            ],
            IntentType.PLOT_QUERY: [
                r'剧情.*发展.*如何',
                r'故事.*进展.*怎样',
                r'情节.*走向.*什么',
                r'主线.*剧情.*介绍',
                r'故事.*梗概.*总结',
            ],
            IntentType.WORLD_QUERY: [
                r'世界观.*设定.*什么',
                r'背景.*设定.*介绍',
                r'世界.*规则.*如何',
                r'魔法.*系统.*怎样',
                r'世界.*背景.*说明',
            ],
            IntentType.RELATIONSHIP_QUERY: [
                r'(.+?)和(.+?).*关系',
                r'(.+?)与(.+?).*关系',
                r'角色.*关系.*网络',
                r'人物.*关系.*图',
                r'(.+?)的.*朋友.*敌人',
            ],
            IntentType.SUMMARY_REQUEST: [
                r'总结.*一下',
                r'概括.*内容',
                r'梳理.*情况',
                r'整理.*信息',
                r'汇总.*资料',
            ],
            IntentType.ANALYSIS_REQUEST: [
                r'分析.*一下',
                r'解读.*内容',
                r'评价.*如何',
                r'点评.*情况',
                r'深入.*分析',
            ],
            IntentType.WRITING_HELP: [
                r'怎么.*写',
                r'如何.*描述',
                r'写作.*建议',
                r'创作.*灵感',
                r'续写.*帮助',
            ]
        }
        
        # 实体提取模式
        self.entity_patterns = {
            'chapter_number': r'第?(\d+)章',
            'character_name': r'([^\s，。！？]+?)(?:这个角色|的性格|在.*章|这个人)',
            'relationship_pair': r'([^\s，。！？]+?)(?:和|与)([^\s，。！？]+?).*关系',
        }
    
    async def process_user_query(self, project_id: str, user_query: str) -> AgentResponse:
        """处理用户查询"""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"处理用户查询: {user_query}")
            
            # 1. 意图识别
            intent = self._recognize_intent(user_query)
            self.logger.info(f"识别意图: {intent.intent_type}, 置信度: {intent.confidence}")
            
            # 2. 规划动作
            actions = self._plan_actions(intent, project_id)
            self.logger.info(f"规划了 {len(actions)} 个动作")
            
            # 3. 执行动作
            retrieved_info = await self._execute_actions(actions, project_id)
            
            # 4. 生成响应
            response_text = await self._generate_response(intent, retrieved_info, project_id)
            
            # 5. 生成建议
            suggestions = self._generate_suggestions(intent, retrieved_info)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return AgentResponse(
                success=True,
                response_text=response_text,
                intent=intent,
                actions_taken=actions,
                retrieved_info=retrieved_info,
                confidence=intent.confidence,
                suggestions=suggestions,
                execution_time=processing_time
            )
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"处理用户查询失败: {e}")
            
            return AgentResponse(
                success=False,
                response_text=f"抱歉，处理您的查询时出现了错误：{str(e)}",
                intent=UserIntent(IntentType.GENERAL_CHAT, 0.0, {}, {}, user_query),
                actions_taken=[],
                retrieved_info={},
                confidence=0.0,
                execution_time=processing_time
            )
    
    def _recognize_intent(self, user_query: str) -> UserIntent:
        """识别用户意图"""
        try:
            best_intent = IntentType.GENERAL_CHAT
            best_confidence = 0.0
            entities = {}
            parameters = {}
            
            # 遍历所有意图模式
            for intent_type, patterns in self.intent_patterns.items():
                for pattern in patterns:
                    match = re.search(pattern, user_query, re.IGNORECASE)
                    if match:
                        confidence = 0.8  # 基础置信度
                        
                        # 提取实体
                        if intent_type == IntentType.CHAPTER_QUERY:
                            chapter_num = match.group(1) if match.groups() else None
                            if chapter_num:
                                entities['chapter_number'] = [int(chapter_num)]
                                confidence += 0.1
                        
                        elif intent_type == IntentType.CHARACTER_QUERY:
                            char_name = match.group(1) if match.groups() else None
                            if char_name:
                                entities['character_name'] = [char_name.strip()]
                                confidence += 0.1
                        
                        elif intent_type == IntentType.RELATIONSHIP_QUERY:
                            if len(match.groups()) >= 2:
                                char1, char2 = match.group(1), match.group(2)
                                entities['character_names'] = [char1.strip(), char2.strip()]
                                confidence += 0.1
                        
                        if confidence > best_confidence:
                            best_intent = intent_type
                            best_confidence = confidence
                            break
            
            # 额外的实体提取
            for entity_type, pattern in self.entity_patterns.items():
                matches = re.findall(pattern, user_query, re.IGNORECASE)
                if matches:
                    if entity_type == 'chapter_number':
                        entities['chapter_number'] = [int(m) for m in matches]
                    elif entity_type == 'character_name':
                        entities['character_name'] = [m.strip() for m in matches]
                    elif entity_type == 'relationship_pair':
                        entities['character_names'] = [m[0].strip(), m[1].strip() for m in matches]
            
            return UserIntent(
                intent_type=best_intent,
                confidence=best_confidence,
                entities=entities,
                parameters=parameters,
                original_query=user_query
            )
            
        except Exception as e:
            self.logger.error(f"意图识别失败: {e}")
            return UserIntent(IntentType.GENERAL_CHAT, 0.0, {}, {}, user_query)
    
    def _plan_actions(self, intent: UserIntent, project_id: str) -> List[AgentAction]:
        """规划执行动作"""
        try:
            actions = []
            
            if intent.intent_type == IntentType.CHAPTER_QUERY:
                # 章节查询动作
                if 'chapter_number' in intent.entities:
                    chapter_nums = intent.entities['chapter_number']
                    actions.append(AgentAction(
                        action_type="search_chapter_by_number",
                        description=f"搜索第{chapter_nums[0]}章的内容",
                        parameters={'chapter_numbers': chapter_nums},
                        priority=5
                    ))
                else:
                    actions.append(AgentAction(
                        action_type="search_chapters_semantic",
                        description="语义搜索相关章节",
                        parameters={'query': intent.original_query},
                        priority=4
                    ))
            
            elif intent.intent_type == IntentType.CHARACTER_QUERY:
                # 角色查询动作
                if 'character_name' in intent.entities:
                    char_names = intent.entities['character_name']
                    actions.append(AgentAction(
                        action_type="search_character_info",
                        description=f"搜索角色 {char_names[0]} 的信息",
                        parameters={'character_names': char_names},
                        priority=5
                    ))
                    actions.append(AgentAction(
                        action_type="search_chapters_by_character",
                        description=f"搜索包含角色 {char_names[0]} 的章节",
                        parameters={'character_names': char_names},
                        priority=4
                    ))
            
            elif intent.intent_type == IntentType.WORLD_QUERY:
                # 世界观查询动作
                actions.append(AgentAction(
                    action_type="search_world_info",
                    description="搜索世界观设定信息",
                    parameters={'info_types': ['world']},
                    priority=5
                ))
            
            elif intent.intent_type == IntentType.PLOT_QUERY:
                # 情节查询动作
                actions.append(AgentAction(
                    action_type="search_plot_info",
                    description="搜索情节发展信息",
                    parameters={'info_types': ['plot']},
                    priority=5
                ))
            
            elif intent.intent_type == IntentType.RELATIONSHIP_QUERY:
                # 关系查询动作
                if 'character_names' in intent.entities:
                    char_names = intent.entities['character_names']
                    actions.append(AgentAction(
                        action_type="search_relationship_info",
                        description=f"搜索 {char_names[0]} 和 {char_names[1]} 的关系",
                        parameters={'character_names': char_names},
                        priority=5
                    ))
                else:
                    actions.append(AgentAction(
                        action_type="search_character_network",
                        description="搜索角色关系网络",
                        parameters={'info_types': ['character']},
                        priority=4
                    ))
            
            # 通用动作：搜索相关记忆
            actions.append(AgentAction(
                action_type="search_memories",
                description="搜索相关记忆信息",
                parameters={'query': intent.original_query},
                priority=3
            ))
            
            # 按优先级排序
            actions.sort(key=lambda x: x.priority, reverse=True)
            
            return actions
            
        except Exception as e:
            self.logger.error(f"规划动作失败: {e}")
            return []

    async def _execute_actions(self, actions: List[AgentAction], project_id: str) -> Dict[str, Any]:
        """执行动作"""
        try:
            retrieved_info = {}

            for action in actions:
                self.logger.info(f"执行动作: {action.action_type}")

                try:
                    if action.action_type == "search_chapter_by_number":
                        result = await self._search_chapter_by_number(project_id, action.parameters)
                        retrieved_info['chapter_info'] = result

                    elif action.action_type == "search_chapters_semantic":
                        result = await self._search_chapters_semantic(project_id, action.parameters)
                        retrieved_info['chapter_search'] = result

                    elif action.action_type == "search_character_info":
                        result = await self._search_character_info(project_id, action.parameters)
                        retrieved_info['character_info'] = result

                    elif action.action_type == "search_chapters_by_character":
                        result = await self._search_chapters_by_character(project_id, action.parameters)
                        retrieved_info['character_chapters'] = result

                    elif action.action_type == "search_world_info":
                        result = await self._search_project_info(project_id, action.parameters)
                        retrieved_info['world_info'] = result

                    elif action.action_type == "search_plot_info":
                        result = await self._search_project_info(project_id, action.parameters)
                        retrieved_info['plot_info'] = result

                    elif action.action_type == "search_relationship_info":
                        result = await self._search_relationship_info(project_id, action.parameters)
                        retrieved_info['relationship_info'] = result

                    elif action.action_type == "search_character_network":
                        result = await self._search_project_info(project_id, action.parameters)
                        retrieved_info['character_network'] = result

                    elif action.action_type == "search_memories":
                        result = await self._search_memories(project_id, action.parameters)
                        retrieved_info['memories'] = result

                except Exception as e:
                    self.logger.error(f"执行动作 {action.action_type} 失败: {e}")
                    continue

            return retrieved_info

        except Exception as e:
            self.logger.error(f"执行动作失败: {e}")
            return {}

    async def _search_chapter_by_number(self, project_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """根据章节号搜索章节"""
        try:
            chapter_numbers = parameters.get('chapter_numbers', [])
            if not chapter_numbers:
                return {}

            chapter_number = chapter_numbers[0]

            # 搜索章节索引
            chapter_index = await self.chapter_index.get_chapter_by_number(project_id, chapter_number)
            if not chapter_index:
                return {'error': f'未找到第{chapter_number}章'}

            # 获取章节详细内容
            chapter = chapter_service.get_chapter(project_id, chapter_index.chapter_id)
            if not chapter:
                return {'error': f'无法获取第{chapter_number}章的详细内容'}

            return {
                'chapter_index': asdict(chapter_index),
                'chapter_content': {
                    'title': chapter.title,
                    'content': chapter.content[:1000] + '...' if len(chapter.content) > 1000 else chapter.content,
                    'word_count': len(chapter.content)
                }
            }

        except Exception as e:
            self.logger.error(f"根据章节号搜索失败: {e}")
            return {'error': str(e)}

    async def _search_chapters_semantic(self, project_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """语义搜索章节"""
        try:
            query = parameters.get('query', '')

            search_query = ChapterSearchQuery(
                project_id=project_id,
                query_text=query,
                search_type="semantic",
                limit=5
            )

            results = await self.chapter_index.search_chapters(search_query)

            return {
                'query': query,
                'results': [asdict(result) for result in results],
                'count': len(results)
            }

        except Exception as e:
            self.logger.error(f"语义搜索章节失败: {e}")
            return {'error': str(e)}

    async def _search_character_info(self, project_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """搜索角色信息"""
        try:
            character_names = parameters.get('character_names', [])
            if not character_names:
                return {}

            character_name = character_names[0]

            # 从角色服务获取基本信息
            characters = character_service.get_characters(project_id)
            target_char = None
            for char in characters:
                if char.name == character_name:
                    target_char = char
                    break

            # 从记忆中搜索详细信息
            memories = await self.ai_memory.search_memories(
                project_id=project_id,
                query=f"角色 {character_name}",
                memory_types=[MemoryType.CHARACTER],
                limit=10
            )

            return {
                'character_name': character_name,
                'basic_info': asdict(target_char) if target_char else None,
                'detailed_memories': [asdict(memory) for memory in memories],
                'memory_count': len(memories)
            }

        except Exception as e:
            self.logger.error(f"搜索角色信息失败: {e}")
            return {'error': str(e)}

    async def _search_chapters_by_character(self, project_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """搜索包含指定角色的章节"""
        try:
            character_names = parameters.get('character_names', [])
            if not character_names:
                return {}

            character_name = character_names[0]

            chapters = await self.chapter_index.get_chapters_by_character(project_id, character_name)

            return {
                'character_name': character_name,
                'chapters': [asdict(chapter) for chapter in chapters],
                'count': len(chapters)
            }

        except Exception as e:
            self.logger.error(f"搜索角色章节失败: {e}")
            return {'error': str(e)}

    async def _search_project_info(self, project_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """搜索项目信息"""
        try:
            info_types = parameters.get('info_types', ['world', 'character', 'plot', 'theme'])
            query = parameters.get('query', '')

            search_query = ProjectInfoSearchQuery(
                project_id=project_id,
                query_text=query,
                info_types=info_types,
                limit=10
            )

            results = await self.project_service.search_project_info(search_query)

            return {
                'info_types': info_types,
                'query': query,
                'results': [asdict(result) for result in results],
                'count': len(results)
            }

        except Exception as e:
            self.logger.error(f"搜索项目信息失败: {e}")
            return {'error': str(e)}

    async def _search_relationship_info(self, project_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """搜索关系信息"""
        try:
            character_names = parameters.get('character_names', [])
            if len(character_names) < 2:
                return {}

            char1, char2 = character_names[0], character_names[1]

            # 搜索关系相关的记忆
            memories = await self.ai_memory.search_memories(
                project_id=project_id,
                query=f"{char1} {char2} 关系",
                memory_types=[MemoryType.CHARACTER, MemoryType.RELATIONSHIP],
                limit=10
            )

            return {
                'character_pair': [char1, char2],
                'relationship_memories': [asdict(memory) for memory in memories],
                'count': len(memories)
            }

        except Exception as e:
            self.logger.error(f"搜索关系信息失败: {e}")
            return {'error': str(e)}

    async def _search_memories(self, project_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """搜索记忆"""
        try:
            query = parameters.get('query', '')

            memories = await self.ai_memory.search_memories(
                project_id=project_id,
                query=query,
                limit=15
            )

            return {
                'query': query,
                'memories': [asdict(memory) for memory in memories],
                'count': len(memories)
            }

        except Exception as e:
            self.logger.error(f"搜索记忆失败: {e}")
            return {'error': str(e)}

    async def _generate_response(self, intent: UserIntent, retrieved_info: Dict[str, Any], project_id: str) -> str:
        """生成响应"""
        try:
            # 构建上下文信息
            context = self._build_context(intent, retrieved_info)

            # 构建提示词
            prompt = f"""
你是一个小说创作助手AI，用户询问了关于他们小说项目的问题。

用户问题：{intent.original_query}
识别的意图：{intent.intent_type.value}

相关信息：
{context}

请根据以上信息，用自然、友好的语言回答用户的问题。如果信息不足，请诚实说明，并建议用户如何获取更多信息。

回答要求：
1. 直接回答用户的问题
2. 提供具体、有用的信息
3. 语言自然流畅
4. 如果有多个相关结果，要有条理地组织
5. 适当提供额外的有价值信息
"""

            # 调用LLM生成响应
            response = await ai_model_service.generate_text(prompt)

            return response.strip()

        except Exception as e:
            self.logger.error(f"生成响应失败: {e}")
            return self._generate_fallback_response(intent, retrieved_info)

    def _build_context(self, intent: UserIntent, retrieved_info: Dict[str, Any]) -> str:
        """构建上下文信息"""
        try:
            context_parts = []

            # 章节信息
            if 'chapter_info' in retrieved_info:
                chapter_info = retrieved_info['chapter_info']
                if 'chapter_index' in chapter_info:
                    index = chapter_info['chapter_index']
                    context_parts.append(f"章节信息：第{index.get('chapter_number', '?')}章 - {index.get('title', '')}")
                    context_parts.append(f"字数：{index.get('word_count', 0)}字")
                    context_parts.append(f"主要角色：{', '.join(index.get('main_characters', []))}")
                    context_parts.append(f"场景：{', '.join(index.get('scenes', []))}")
                    context_parts.append(f"情感基调：{index.get('emotional_tone', '')}")

                if 'chapter_content' in chapter_info:
                    content = chapter_info['chapter_content']
                    context_parts.append(f"章节内容摘要：{content.get('content', '')[:200]}...")

            # 角色信息
            if 'character_info' in retrieved_info:
                char_info = retrieved_info['character_info']
                char_name = char_info.get('character_name', '')
                context_parts.append(f"角色信息：{char_name}")

                if 'basic_info' in char_info and char_info['basic_info']:
                    basic = char_info['basic_info']
                    context_parts.append(f"角色描述：{basic.get('description', '')}")
                    context_parts.append(f"性格特征：{', '.join(basic.get('personality_tags', []))}")

                memory_count = char_info.get('memory_count', 0)
                if memory_count > 0:
                    context_parts.append(f"相关记忆：{memory_count}条")

            # 世界观信息
            if 'world_info' in retrieved_info:
                world_info = retrieved_info['world_info']
                results = world_info.get('results', [])
                if results:
                    context_parts.append("世界观设定：")
                    for result in results[:3]:  # 只显示前3个结果
                        context_parts.append(f"- {result.get('title', '')}: {result.get('content', '')[:100]}...")

            # 情节信息
            if 'plot_info' in retrieved_info:
                plot_info = retrieved_info['plot_info']
                results = plot_info.get('results', [])
                if results:
                    context_parts.append("情节信息：")
                    for result in results[:3]:
                        context_parts.append(f"- {result.get('title', '')}: {result.get('content', '')[:100]}...")

            # 章节搜索结果
            if 'chapter_search' in retrieved_info:
                search_results = retrieved_info['chapter_search']
                results = search_results.get('results', [])
                if results:
                    context_parts.append("相关章节：")
                    for result in results[:3]:
                        chapter_index = result.get('chapter_index', {})
                        context_parts.append(f"- 第{chapter_index.get('chapter_number', '?')}章: {chapter_index.get('title', '')}")

            # 记忆信息
            if 'memories' in retrieved_info:
                memories = retrieved_info['memories']
                memory_list = memories.get('memories', [])
                if memory_list:
                    context_parts.append("相关记忆：")
                    for memory in memory_list[:3]:
                        context_parts.append(f"- {memory.get('title', '')}: {memory.get('summary', '')}")

            return '\n'.join(context_parts)

        except Exception as e:
            self.logger.error(f"构建上下文失败: {e}")
            return "无法构建上下文信息"

    def _generate_fallback_response(self, intent: UserIntent, retrieved_info: Dict[str, Any]) -> str:
        """生成备用响应"""
        try:
            if intent.intent_type == IntentType.CHAPTER_QUERY:
                if 'chapter_info' in retrieved_info:
                    chapter_info = retrieved_info['chapter_info']
                    if 'error' in chapter_info:
                        return f"抱歉，{chapter_info['error']}"

                    if 'chapter_index' in chapter_info:
                        index = chapter_info['chapter_index']
                        return f"第{index.get('chapter_number', '?')}章《{index.get('title', '')}》包含{len(index.get('main_characters', []))}个主要角色，字数约{index.get('word_count', 0)}字。"

                return "抱歉，没有找到相关的章节信息。"

            elif intent.intent_type == IntentType.CHARACTER_QUERY:
                if 'character_info' in retrieved_info:
                    char_info = retrieved_info['character_info']
                    char_name = char_info.get('character_name', '')
                    memory_count = char_info.get('memory_count', 0)
                    return f"关于角色{char_name}，我找到了{memory_count}条相关记忆。"

                return "抱歉，没有找到相关的角色信息。"

            elif intent.intent_type == IntentType.WORLD_QUERY:
                if 'world_info' in retrieved_info:
                    world_info = retrieved_info['world_info']
                    count = world_info.get('count', 0)
                    return f"关于世界观设定，我找到了{count}条相关信息。"

                return "抱歉，没有找到相关的世界观信息。"

            else:
                total_results = sum(
                    info.get('count', 0) if isinstance(info, dict) else 0
                    for info in retrieved_info.values()
                )
                return f"我找到了{total_results}条相关信息，但无法生成详细回答。请尝试更具体的问题。"

        except Exception as e:
            self.logger.error(f"生成备用响应失败: {e}")
            return "抱歉，处理您的问题时遇到了困难。"

    def _generate_suggestions(self, intent: UserIntent, retrieved_info: Dict[str, Any]) -> List[str]:
        """生成建议"""
        try:
            suggestions = []

            if intent.intent_type == IntentType.CHAPTER_QUERY:
                suggestions.extend([
                    "您可以询问其他章节的内容",
                    "可以问问这一章中的角色表现如何",
                    "可以了解这一章的情节发展"
                ])

            elif intent.intent_type == IntentType.CHARACTER_QUERY:
                suggestions.extend([
                    "可以询问这个角色在其他章节的表现",
                    "可以了解这个角色与其他角色的关系",
                    "可以问问这个角色的发展轨迹"
                ])

            elif intent.intent_type == IntentType.WORLD_QUERY:
                suggestions.extend([
                    "可以了解更多世界观的具体设定",
                    "可以询问魔法系统或技术水平",
                    "可以了解世界的历史背景"
                ])

            elif intent.intent_type == IntentType.PLOT_QUERY:
                suggestions.extend([
                    "可以询问具体章节的情节发展",
                    "可以了解主要冲突和转折点",
                    "可以问问故事的主题思想"
                ])

            # 根据检索到的信息添加动态建议
            if 'character_info' in retrieved_info:
                char_name = retrieved_info['character_info'].get('character_name', '')
                if char_name:
                    suggestions.append(f"可以询问{char_name}在其他章节的表现")

            if 'chapter_info' in retrieved_info:
                chapter_num = retrieved_info['chapter_info'].get('chapter_index', {}).get('chapter_number')
                if chapter_num:
                    suggestions.append(f"可以询问第{chapter_num + 1}章的内容")
                    if chapter_num > 1:
                        suggestions.append(f"可以回顾第{chapter_num - 1}章的内容")

            return suggestions[:5]  # 最多返回5个建议

        except Exception as e:
            self.logger.error(f"生成建议失败: {e}")
            return ["可以尝试问问其他相关问题"]


# 全局实例
intelligent_agent_service = IntelligentAgentService()


def get_intelligent_agent_service() -> IntelligentAgentService:
    """获取智能Agent服务实例"""
    return intelligent_agent_service
